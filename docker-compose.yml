version: '3.8'

services:
  # NSE Insider Trading API
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://nsescreaper_owner:<EMAIL>/nsescreaper?sslmode=require
      - PYTHONPATH=/app
    volumes:
      - ./logs:/app/logs
      - ./monitoring/logs:/app/monitoring/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NSE Data Scraper (scheduled)
  scraper:
    build: .
    environment:
      - DATABASE_URL=postgresql://nsescreaper_owner:<EMAIL>/nsescreaper?sslmode=require
      - PYTHONPATH=/app
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    command: python scraper/scheduler.py
    depends_on:
      - api

  # Performance Monitor
  monitor:
    build: .
    environment:
      - DATABASE_URL=postgresql://nsescreaper_owner:<EMAIL>/nsescreaper?sslmode=require
      - PYTHONPATH=/app
    volumes:
      - ./monitoring/logs:/app/monitoring/logs
      - ./config:/app/config
    restart: unless-stopped
    command: python monitoring/performance_monitor.py
    depends_on:
      - api

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./frontend/build:/usr/share/nginx/html:ro
    restart: unless-stopped
    depends_on:
      - api

volumes:
  redis_data:

networks:
  default:
    name: nse-insider-trading

