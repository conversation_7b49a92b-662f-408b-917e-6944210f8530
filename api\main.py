"""
FastAPI Backend for NSE Insider Trading System
Features:
- RESTful API endpoints for data access
- Authentication and authorization
- Rate limiting and caching
- Analytics and aggregation endpoints
- Real-time data status
- Comprehensive API documentation
"""

from fastapi import FastAPI, HTTPException, Depends, Query, Path, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, List, Dict, Any
import json
from pathlib import Path as PathLib

# Import our modules
import sys
sys.path.append(str(PathLib(__file__).parent.parent))
from database.connection import db_manager, data_access
from api.models import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting NSE Insider Trading API...")
    await db_manager.initialize_async_pool()
    logger.info("API startup completed")

    yield

    # Shutdown
    logger.info("Shutting down NSE Insider Trading API...")
    await db_manager.close_pools()
    logger.info("API shutdown completed")

# Create FastAPI app
app = FastAPI(
    title="NSE Insider Trading API",
    description="Comprehensive API for NSE insider trading data analysis and visualization",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
import os

# Get environment-specific configuration
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

# Configure CORS origins based on environment
if ENVIRONMENT == "production":
    # Production: Allow same-origin requests and Cloud Run domains
    cors_origins = [
        "https://nse-insider-trading-497899569447.asia-south1.run.app",
        "https://*.run.app",  # Allow any Cloud Run domain
        "https://*.googleapis.com",  # Allow Google services
        "http://localhost:3000",  # Keep for local development
        "http://localhost:3001",  # Keep for local development
    ]
    # Production: Allow Cloud Run hosts and be more permissive
    trusted_hosts = [
        "*",  # Allow all hosts in production for Cloud Run flexibility
    ]
else:
    # Development: Local only
    cors_origins = ["http://localhost:3000", "http://localhost:3001"]
    trusted_hosts = ["localhost", "127.0.0.1", "*.localhost"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins if ENVIRONMENT != "production" else ["*"],  # Allow all origins in production for same-domain requests
    allow_origin_regex=r"https://.*\.run\.app" if ENVIRONMENT == "production" else None,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=trusted_hosts
)

# Mount static files for React frontend
import os
frontend_build_path = os.path.join(os.path.dirname(__file__), "..", "frontend", "build")
if os.path.exists(frontend_build_path):
    app.mount("/static", StaticFiles(directory=os.path.join(frontend_build_path, "static")), name="static")

# Simple in-memory cache for basic caching
cache = {}

async def get_cached_or_execute(cache_key: str, func, expire_seconds: int = 300):
    """Simple caching helper"""
    import time

    if cache_key in cache:
        cached_data, timestamp = cache[cache_key]
        if time.time() - timestamp < expire_seconds:
            return cached_data

    result = await func()
    cache[cache_key] = (result, time.time())
    return result

def parse_date_string(date_str: str) -> datetime:
    """Parse date string to datetime object"""
    if not date_str:
        return None

    try:
        # Try parsing as date only (YYYY-MM-DD)
        if len(date_str) == 10 and 'T' not in date_str:
            return datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        # Try parsing as datetime
        else:
            # Handle various datetime formats
            for fmt in ["%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S.%f"]:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.replace(tzinfo=timezone.utc)
                except ValueError:
                    continue
            # If no format matches, try ISO format
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
    except (ValueError, TypeError) as e:
        logger.warning(f"Failed to parse date string '{date_str}': {e}")
        return None

# Root endpoint - serve React frontend
@app.get("/", include_in_schema=False)
async def serve_frontend():
    """Serve React frontend"""
    frontend_build_path = os.path.join(os.path.dirname(__file__), "..", "frontend", "build")
    index_file = os.path.join(frontend_build_path, "index.html")

    if os.path.exists(index_file):
        return FileResponse(index_file)
    else:
        # Fallback to API info if frontend not available
        return {
            "name": "NSE Insider Trading API",
            "version": "1.0.0",
            "description": "Comprehensive API for NSE insider trading data analysis and visualization",
            "status": "running",
            "endpoints": {
                "health": "/health",
                "docs": "/docs",
                "transactions": "/transactions",
                "analytics": "/analytics/summary",
                "search": "/search"
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# API info endpoint
@app.get("/api", tags=["System"])
async def api_info():
    """API information endpoint"""
    return {
        "name": "NSE Insider Trading API",
        "version": "1.0.0",
        "description": "Comprehensive API for NSE insider trading data analysis and visualization",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "transactions": "/transactions",
            "analytics": "/analytics/summary",
            "search": "/search"
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

# Health check endpoint
@app.get("/health", tags=["System"])
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connectivity
        async with db_manager.get_async_connection() as conn:
            await conn.fetchval("SELECT 1")
        
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "database": "connected",
            "cache": "active"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }
        )

# Public API - No authentication required

# Data endpoints
@app.get("/transactions", response_model=TransactionListResponse, tags=["Transactions"])
async def get_transactions(
    symbol: Optional[str] = Query(None, description="Company symbol filter"),
    person_name: Optional[str] = Query(None, description="Person name filter"),
    person_category: Optional[str] = Query(None, description="Person category filter"),
    transaction_type: Optional[str] = Query(None, description="Transaction type filter"),
    from_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)"),
    to_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)"),
    min_value: Optional[float] = Query(None, description="Minimum transaction value"),
    max_value: Optional[float] = Query(None, description="Maximum transaction value"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=1000, description="Items per page"),
    sort_by: str = Query("transaction_date", description="Sort field"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="Sort order")
):
    """Get insider trading transactions with filtering and pagination"""
    try:
        # Parse date strings
        parsed_from_date = parse_date_string(from_date) if from_date else None
        parsed_to_date = parse_date_string(to_date) if to_date else None

        # Build cache key
        cache_key = f"transactions:{hash(str(locals()))}"

        # Try cache first
        if cache_key in cache:
            cached_data, timestamp = cache[cache_key]
            import time
            if time.time() - timestamp < 300:  # 5 minutes cache
                return cached_data
        
        # Build query
        query_parts = []
        params = []
        param_count = 0
        
        base_query = """
            SELECT 
                it.id,
                c.symbol,
                c.company_name,
                it.person_name,
                pc.category_name as person_category,
                it.transaction_date,
                it.intimation_date,
                tt.type_name as transaction_type,
                tm.mode_name as transaction_mode,
                st.type_name as security_type,
                e.exchange_name,
                it.buy_value,
                it.sell_value,
                it.buy_quantity,
                it.sell_quantity,
                it.security_value,
                it.securities_acquired,
                it.shares_before_transaction,
                it.percentage_before_transaction,
                it.shares_after_transaction,
                it.percentage_after_transaction,
                it.remarks,
                it.xbrl_link,
                it.created_at
            FROM insider_transactions it
            JOIN companies c ON it.company_id = c.id
            LEFT JOIN person_categories pc ON it.person_category_id = pc.id
            LEFT JOIN transaction_types tt ON it.transaction_type_id = tt.id
            LEFT JOIN transaction_modes tm ON it.transaction_mode_id = tm.id
            LEFT JOIN security_types st ON it.security_type_id = st.id
            LEFT JOIN exchanges e ON it.exchange_id = e.id
        """
        
        # Add filters
        if symbol:
            param_count += 1
            query_parts.append(f"c.symbol ILIKE ${param_count}")
            params.append(f"%{symbol}%")
        
        if person_name:
            param_count += 1
            query_parts.append(f"it.person_name ILIKE ${param_count}")
            params.append(f"%{person_name}%")
        
        if person_category:
            param_count += 1
            query_parts.append(f"pc.category_name ILIKE ${param_count}")
            params.append(f"%{person_category}%")
        
        if transaction_type:
            param_count += 1
            query_parts.append(f"tt.type_name ILIKE ${param_count}")
            params.append(f"%{transaction_type}%")
        
        if parsed_from_date:
            param_count += 1
            query_parts.append(f"it.transaction_date >= ${param_count}")
            params.append(parsed_from_date)

        if parsed_to_date:
            param_count += 1
            query_parts.append(f"it.transaction_date <= ${param_count}")
            params.append(parsed_to_date)
        
        if min_value:
            param_count += 1
            query_parts.append(f"it.security_value >= ${param_count}")
            params.append(min_value)
        
        if max_value:
            param_count += 1
            query_parts.append(f"it.security_value <= ${param_count}")
            params.append(max_value)
        
        # Build WHERE clause
        where_clause = ""
        if query_parts:
            where_clause = "WHERE " + " AND ".join(query_parts)
        
        # Add sorting and pagination
        offset = (page - 1) * limit
        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)
        
        param_count += 1
        offset_param = f"${param_count}"
        params.append(offset)
        
        # Final query
        final_query = f"""
            {base_query}
            {where_clause}
            ORDER BY {sort_by} {sort_order.upper()}
            LIMIT {limit_param} OFFSET {offset_param}
        """
        
        # Count query
        count_query = f"""
            SELECT COUNT(*)
            FROM insider_transactions it
            JOIN companies c ON it.company_id = c.id
            LEFT JOIN person_categories pc ON it.person_category_id = pc.id
            LEFT JOIN transaction_types tt ON it.transaction_type_id = tt.id
            LEFT JOIN transaction_modes tm ON it.transaction_mode_id = tm.id
            LEFT JOIN security_types st ON it.security_type_id = st.id
            LEFT JOIN exchanges e ON it.exchange_id = e.id
            {where_clause}
        """
        
        # Execute queries
        async with db_manager.get_async_connection() as conn:
            # Get total count
            total_count = await conn.fetchval(count_query, *params[:-2])  # Exclude limit and offset
            
            # Get transactions
            rows = await conn.fetch(final_query, *params)

            # Convert UUID objects to strings and handle None values
            transactions = []
            for row in rows:
                transaction = dict(row)
                # Convert UUID to string
                if transaction.get('id'):
                    transaction['id'] = str(transaction['id'])
                # Convert other UUID fields if they exist
                if transaction.get('company_id'):
                    transaction['company_id'] = str(transaction['company_id'])
                # Handle None values for numeric fields
                for field in ['buy_value', 'sell_value', 'security_value', 'buy_quantity', 'sell_quantity',
                             'securities_acquired', 'shares_before_transaction', 'shares_after_transaction',
                             'percentage_before_transaction', 'percentage_after_transaction']:
                    if transaction.get(field) is None:
                        transaction[field] = None
                    elif isinstance(transaction.get(field), (int, float)):
                        transaction[field] = float(transaction[field]) if field.endswith('_value') or 'percentage' in field else int(transaction[field])
                transactions.append(transaction)
        
        # Prepare response
        response = TransactionListResponse(
            transactions=transactions,
            total_count=total_count,
            page=page,
            limit=limit,
            total_pages=(total_count + limit - 1) // limit
        )
        
        # Cache result for 5 minutes
        import time
        cache[cache_key] = (response, time.time())
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to get transactions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve transactions"
        )

# Analytics endpoints
@app.get("/analytics/summary", tags=["Analytics"])
async def get_analytics_summary(
    from_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)"),
    to_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)")
):
    """Get summary analytics for insider trading data"""
    try:
        # Parse date strings
        parsed_from_date = parse_date_string(from_date) if from_date else None
        parsed_to_date = parse_date_string(to_date) if to_date else None

        cache_key = f"analytics_summary:{from_date}:{to_date}"

        async def fetch_summary():
            async with db_manager.get_async_connection() as conn:
                # Build date filter
                date_filter = ""
                params = []
                if parsed_from_date:
                    date_filter += " AND it.transaction_date >= $1"
                    params.append(parsed_from_date)
                if parsed_to_date:
                    param_num = len(params) + 1
                    date_filter += f" AND it.transaction_date <= ${param_num}"
                    params.append(parsed_to_date)

                # Get summary statistics
                summary_query = f"""
                    SELECT
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN it.buy_value > 0 THEN it.buy_value ELSE 0 END) as total_buy_value,
                        SUM(CASE WHEN it.sell_value > 0 THEN it.sell_value ELSE 0 END) as total_sell_value,
                        COUNT(DISTINCT it.company_id) as unique_companies,
                        COUNT(DISTINCT it.person_name) as unique_persons,
                        MIN(it.transaction_date) as earliest_date,
                        MAX(it.transaction_date) as latest_date
                    FROM insider_transactions it
                    WHERE 1=1 {date_filter}
                """

                summary = await conn.fetchrow(summary_query, *params)

                total_buy = float(summary["total_buy_value"] or 0)
                total_sell = float(summary["total_sell_value"] or 0)

                return {
                    "total_transactions": summary["total_transactions"],
                    "total_buy_value": total_buy,
                    "total_sell_value": total_sell,
                    "net_value": total_buy - total_sell,
                    "unique_companies": summary["unique_companies"],
                    "unique_persons": summary["unique_persons"],
                    "date_range": {
                        "earliest": summary["earliest_date"],
                        "latest": summary["latest_date"]
                    }
                }

        return await get_cached_or_execute(cache_key, fetch_summary, 600)  # 10 minutes cache

    except Exception as e:
        logger.error(f"Failed to get analytics summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analytics summary"
        )

@app.get("/analytics/top-companies", tags=["Analytics"])
async def get_top_companies(
    limit: int = Query(10, ge=1, le=100, description="Number of companies to return"),
    sort_by: str = Query("total_value", description="Sort by: total_value, transaction_count"),
    from_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)"),
    to_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)")
):
    """Get top companies by insider trading activity"""
    try:
        # Parse date strings
        parsed_from_date = parse_date_string(from_date) if from_date else None
        parsed_to_date = parse_date_string(to_date) if to_date else None

        cache_key = f"top_companies:{limit}:{sort_by}:{from_date}:{to_date}"

        async def fetch_top_companies():
            async with db_manager.get_async_connection() as conn:
                # Build date filter
                date_filter = ""
                params = []
                if parsed_from_date:
                    date_filter += " AND it.transaction_date >= $1"
                    params.append(parsed_from_date)
                if parsed_to_date:
                    param_num = len(params) + 1
                    date_filter += f" AND it.transaction_date <= ${param_num}"
                    params.append(parsed_to_date)

                # Determine sort column
                sort_column = "total_value" if sort_by == "total_value" else "transaction_count"

                query = f"""
                    SELECT
                        c.symbol,
                        c.company_name,
                        COUNT(*) as transaction_count,
                        SUM(COALESCE(it.security_value, 0)) as total_value,
                        SUM(COALESCE(it.buy_value, 0)) as total_buy_value,
                        SUM(COALESCE(it.sell_value, 0)) as total_sell_value,
                        SUM(COALESCE(it.buy_value, 0)) - SUM(COALESCE(it.sell_value, 0)) as net_value,
                        COUNT(DISTINCT it.person_name) as unique_insiders,
                        MAX(it.transaction_date) as latest_transaction_date
                    FROM insider_transactions it
                    JOIN companies c ON it.company_id = c.id
                    WHERE 1=1 {date_filter}
                    GROUP BY c.id, c.symbol, c.company_name
                    ORDER BY {sort_column} DESC
                    LIMIT ${len(params) + 1}
                """

                params.append(limit)
                rows = await conn.fetch(query, *params)

                return [dict(row) for row in rows]

        return await get_cached_or_execute(cache_key, fetch_top_companies, 600)

    except Exception as e:
        logger.error(f"Failed to get top companies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve top companies"
        )

@app.get("/companies/{symbol}/transactions", tags=["Companies"])
async def get_company_transactions(
    symbol: str = Path(..., description="Company symbol"),
    from_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)"),
    to_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS)"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=500, description="Items per page")
):
    """Get transactions for a specific company"""
    try:
        # Parse date strings
        parsed_from_date = parse_date_string(from_date) if from_date else None
        parsed_to_date = parse_date_string(to_date) if to_date else None

        cache_key = f"company_transactions:{symbol}:{from_date}:{to_date}:{page}:{limit}"

        async def fetch_company_transactions():
            async with db_manager.get_async_connection() as conn:
                # Build date filter
                date_filter = ""
                params = [symbol.upper()]
                param_count = 1

                if parsed_from_date:
                    param_count += 1
                    date_filter += f" AND it.transaction_date >= ${param_count}"
                    params.append(parsed_from_date)
                if parsed_to_date:
                    param_count += 1
                    date_filter += f" AND it.transaction_date <= ${param_count}"
                    params.append(parsed_to_date)

                # Get transactions
                offset = (page - 1) * limit
                param_count += 1
                limit_param = f"${param_count}"
                params.append(limit)

                param_count += 1
                offset_param = f"${param_count}"
                params.append(offset)

                query = f"""
                    SELECT
                        it.id,
                        c.symbol,
                        c.company_name,
                        it.person_name,
                        pc.category_name as person_category,
                        it.transaction_date,
                        tt.type_name as transaction_type,
                        tm.mode_name as transaction_mode,
                        it.buy_value,
                        it.sell_value,
                        it.security_value,
                        it.buy_quantity,
                        it.sell_quantity,
                        it.securities_acquired,
                        it.percentage_before_transaction,
                        it.percentage_after_transaction,
                        it.remarks
                    FROM insider_transactions it
                    JOIN companies c ON it.company_id = c.id
                    LEFT JOIN person_categories pc ON it.person_category_id = pc.id
                    LEFT JOIN transaction_types tt ON it.transaction_type_id = tt.id
                    LEFT JOIN transaction_modes tm ON it.transaction_mode_id = tm.id
                    WHERE c.symbol = $1 {date_filter}
                    ORDER BY it.transaction_date DESC
                    LIMIT {limit_param} OFFSET {offset_param}
                """

                # Count query
                count_query = f"""
                    SELECT COUNT(*)
                    FROM insider_transactions it
                    JOIN companies c ON it.company_id = c.id
                    WHERE c.symbol = $1 {date_filter}
                """

                total_count = await conn.fetchval(count_query, *params[:-2])
                rows = await conn.fetch(query, *params)

                # Convert UUID objects to strings
                transactions = []
                for row in rows:
                    transaction = dict(row)
                    # Convert UUID to string
                    if transaction.get('id'):
                        transaction['id'] = str(transaction['id'])
                    # Handle None values for numeric fields
                    for field in ['buy_value', 'sell_value', 'security_value', 'buy_quantity', 'sell_quantity',
                                 'securities_acquired', 'percentage_before_transaction', 'percentage_after_transaction']:
                        if transaction.get(field) is None:
                            transaction[field] = None
                        elif isinstance(transaction.get(field), (int, float)):
                            transaction[field] = float(transaction[field]) if 'percentage' in field or 'value' in field else int(transaction[field])
                    transactions.append(transaction)

                return {
                    "transactions": transactions,
                    "total_count": total_count,
                    "page": page,
                    "limit": limit,
                    "total_pages": (total_count + limit - 1) // limit
                }

        return await get_cached_or_execute(cache_key, fetch_company_transactions, 300)

    except Exception as e:
        logger.error(f"Failed to get company transactions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve company transactions"
        )

@app.get("/search", tags=["Search"])
async def search(
    q: str = Query(..., min_length=2, description="Search query"),
    type: str = Query("all", pattern="^(all|company|person)$", description="Search type"),
    limit: int = Query(10, ge=1, le=50, description="Number of results")
):
    """Search companies and persons"""
    try:
        cache_key = f"search:{q}:{type}:{limit}"

        async def perform_search():
            async with db_manager.get_async_connection() as conn:
                results = []

                # Search companies
                if type in ["all", "company"]:
                    company_query = """
                        SELECT 'company' as type, id, symbol, company_name as name
                        FROM companies
                        WHERE symbol ILIKE $1 OR company_name ILIKE $1
                        ORDER BY
                            CASE WHEN symbol ILIKE $1 THEN 1 ELSE 2 END,
                            company_name
                        LIMIT $2
                    """
                    company_results = await conn.fetch(company_query, f"%{q}%", limit)
                    # Convert UUID objects to strings for company results
                    for row in company_results:
                        result = dict(row)
                        if result.get('id'):
                            result['id'] = str(result['id'])
                        results.append(result)

                # Search persons
                if type in ["all", "person"]:
                    person_query = """
                        SELECT DISTINCT
                            'person' as type,
                            person_name as name,
                            pc.category_name as category,
                            COUNT(*) as transaction_count
                        FROM insider_transactions it
                        LEFT JOIN person_categories pc ON it.person_category_id = pc.id
                        WHERE person_name ILIKE $1
                        GROUP BY person_name, pc.category_name
                        ORDER BY transaction_count DESC
                        LIMIT $2
                    """
                    person_results = await conn.fetch(person_query, f"%{q}%", limit)
                    results.extend([dict(row) for row in person_results])

                return {
                    "results": results[:limit],
                    "total_count": len(results),
                    "query": q,
                    "search_type": type
                }

        return await get_cached_or_execute(cache_key, perform_search, 600)

    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search failed"
        )

@app.post("/system/scrape", tags=["System"])
async def trigger_manual_scrape(
    days_back: int = Query(7, ge=1, le=90, description="Number of days to scrape back")
):
    """Trigger manual data scraping"""
    try:
        # Import scraper here to avoid circular imports
        from scraper.enhanced_scraper import EnhancedNSEScraper

        logger.info(f"Manual scrape triggered for {days_back} days")

        # Create scraper instance
        scraper = EnhancedNSEScraper()

        # Perform scrape in background task
        import asyncio
        import threading

        def run_scrape():
            try:
                result = scraper.scrape_data(days_back=days_back)
                logger.info(f"Manual scrape completed: {result.records_inserted} records inserted")
                return result
            except Exception as e:
                logger.error(f"Manual scrape failed: {e}")
                return None

        # Run scrape in thread to avoid blocking
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, run_scrape)

        if result and result.success:
            return {
                "status": "success",
                "message": "Manual scrape completed successfully",
                "records_fetched": result.records_fetched,
                "records_inserted": result.records_inserted,
                "records_skipped": result.records_skipped,
                "execution_time": result.execution_time,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        else:
            error_msg = result.error_message if result else "Unknown error occurred"
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "status": "error",
                    "message": f"Manual scrape failed: {error_msg}",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

    except Exception as e:
        logger.error(f"Failed to trigger manual scrape: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to trigger manual scrape: {str(e)}"
        )

@app.get("/system/status", tags=["System"])
async def get_system_status():
    """Get system status including scraper and database statistics"""
    try:
        async with db_manager.get_async_connection() as conn:
            # Get database statistics
            db_stats = await conn.fetchrow("""
                SELECT
                    COUNT(*) as total_transactions,
                    COUNT(DISTINCT company_id) as total_companies,
                    MIN(transaction_date) as earliest_transaction,
                    MAX(transaction_date) as latest_transaction,
                    MAX(created_at) as last_updated
                FROM insider_transactions
            """)

            # Get latest scraper execution
            scraper_status = await conn.fetchrow("""
                SELECT
                    execution_start,
                    execution_end,
                    status,
                    records_fetched,
                    records_inserted,
                    records_skipped,
                    error_message
                FROM scraper_logs
                ORDER BY execution_start DESC
                LIMIT 1
            """)

            return {
                "api_status": "healthy",
                "database_status": {
                    "total_transactions": db_stats["total_transactions"],
                    "total_companies": db_stats["total_companies"],
                    "date_range": {
                        "earliest": db_stats["earliest_transaction"],
                        "latest": db_stats["latest_transaction"]
                    },
                    "last_updated": db_stats["last_updated"]
                },
                "scraper_status": {
                    "last_execution": scraper_status["execution_start"] if scraper_status else None,
                    "status": scraper_status["status"] if scraper_status else "unknown",
                    "records_fetched": scraper_status["records_fetched"] if scraper_status else 0,
                    "records_inserted": scraper_status["records_inserted"] if scraper_status else 0,
                    "records_skipped": scraper_status["records_skipped"] if scraper_status else 0,
                    "error_message": scraper_status["error_message"] if scraper_status else None
                },
                "timestamp": datetime.now(timezone.utc)
            }

    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system status"
        )

# Catch-all route for React Router (must be last)
@app.get("/{full_path:path}", include_in_schema=False)
async def serve_frontend_routes(full_path: str):
    """Catch-all route to serve React frontend for client-side routing"""
    # Don't intercept API routes
    if full_path.startswith(("health", "transactions", "analytics", "companies", "search", "system", "docs", "redoc", "openapi.json")):
        raise HTTPException(status_code=404, detail="Not Found")

    # Serve React frontend for all other routes
    frontend_build_path = os.path.join(os.path.dirname(__file__), "..", "frontend", "build")
    index_file = os.path.join(frontend_build_path, "index.html")

    if os.path.exists(index_file):
        return FileResponse(index_file)
    else:
        raise HTTPException(status_code=404, detail="Frontend not found")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True)
