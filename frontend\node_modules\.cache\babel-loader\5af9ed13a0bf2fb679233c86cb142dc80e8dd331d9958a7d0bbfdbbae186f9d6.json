{"ast": null, "code": "import axios from'axios';// API base URL - will use proxy in development\nconst API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:8000';// Create axios instance with default config\nconst api=axios.create({baseURL:API_BASE_URL,timeout:30000,headers:{'Content-Type':'application/json'}});// Request interceptor for logging\napi.interceptors.request.use(config=>{var _config$method;console.log(\"API Request: \".concat((_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase(),\" \").concat(config.url));return config;},error=>{console.error('API Request Error:',error);return Promise.reject(error);});// Response interceptor for error handling\napi.interceptors.response.use(response=>{return response;},error=>{console.error('API Response Error:',error);if(error.response){// Server responded with error status\nconst{status,data}=error.response;console.error(\"API Error \".concat(status,\":\"),data);}else if(error.request){// Request was made but no response received\nconsole.error('No response received:',error.request);}else{// Something else happened\nconsole.error('Request setup error:',error.message);}return Promise.reject(error);});// API service interface\n// API service class\nclass ApiService{// Health check\nasync healthCheck(){return api.get('/health');}// Transactions\nasync getTransactions(){let filters=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const params=new URLSearchParams();Object.entries(filters).forEach(_ref=>{let[key,value]=_ref;if(value!==undefined&&value!==null&&value!==''){params.append(key,value.toString());}});return api.get(\"/transactions?\".concat(params.toString()));}// Analytics\nasync getAnalyticsSummary(fromDate,toDate){const params=new URLSearchParams();if(fromDate)params.append('from_date',fromDate);if(toDate)params.append('to_date',toDate);return api.get(\"/analytics/summary?\".concat(params.toString()));}async getTopCompanies(){let options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const params=new URLSearchParams();Object.entries(options).forEach(_ref2=>{let[key,value]=_ref2;if(value!==undefined&&value!==null&&value!==''){params.append(key,value.toString());}});return api.get(\"/analytics/top-companies?\".concat(params.toString()));}// Companies\nasync getCompanyTransactions(symbol){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const params=new URLSearchParams();Object.entries(options).forEach(_ref3=>{let[key,value]=_ref3;if(value!==undefined&&value!==null&&value!==''){params.append(key,value.toString());}});return api.get(\"/companies/\".concat(symbol,\"/transactions?\").concat(params.toString()));}// Search\nasync search(query){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'all';let limit=arguments.length>2&&arguments[2]!==undefined?arguments[2]:10;const params=new URLSearchParams({q:query,type,limit:limit.toString()});return api.get(\"/search?\".concat(params.toString()));}// System status\nasync getSystemStatus(){return api.get('/system/status');}// Manual scraping\nasync triggerManualScrape(){let daysBack=arguments.length>0&&arguments[0]!==undefined?arguments[0]:7;return api.post(\"/system/scrape?days_back=\".concat(daysBack));}// Utility method to format API errors\nformatError(error){var _error$response,_error$response$data,_error$response2,_error$response2$data;if((_error$response=error.response)!==null&&_error$response!==void 0&&(_error$response$data=_error$response.data)!==null&&_error$response$data!==void 0&&_error$response$data.detail){return error.response.data.detail;}else if((_error$response2=error.response)!==null&&_error$response2!==void 0&&(_error$response2$data=_error$response2.data)!==null&&_error$response2$data!==void 0&&_error$response2$data.message){return error.response.data.message;}else if(error.message){return error.message;}else{return'An unexpected error occurred';}}}// Export singleton instance\nexport const apiService=new ApiService();export default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "concat", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "data", "message", "ApiService", "healthCheck", "get", "getTransactions", "filters", "arguments", "length", "undefined", "params", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "_ref", "key", "value", "append", "toString", "getAnalyticsSummary", "fromDate", "toDate", "getTopCompanies", "options", "_ref2", "getCompanyTransactions", "symbol", "_ref3", "search", "query", "type", "limit", "q", "getSystemStatus", "triggerManualScrape", "daysBack", "post", "formatError", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "detail", "apiService"], "sources": ["D:/chirag/nsescrapper/frontend/src/services/apiService.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\n\n// API base URL - will use proxy in development\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for logging\napi.interceptors.request.use(\n  (config) => {\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    console.error('API Response Error:', error);\n    \n    if (error.response) {\n      // Server responded with error status\n      const { status, data } = error.response;\n      console.error(`API Error ${status}:`, data);\n    } else if (error.request) {\n      // Request was made but no response received\n      console.error('No response received:', error.request);\n    } else {\n      // Something else happened\n      console.error('Request setup error:', error.message);\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\n// API service interface\nexport interface TransactionFilters {\n  symbol?: string;\n  person_name?: string;\n  person_category?: string;\n  transaction_type?: string;\n  from_date?: string;\n  to_date?: string;\n  min_value?: number;\n  max_value?: number;\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'asc' | 'desc';\n}\n\nexport interface Transaction {\n  id: string;\n  symbol: string;\n  company_name: string;\n  person_name: string;\n  person_category?: string;\n  transaction_date: string;\n  intimation_date?: string;\n  transaction_type?: string;\n  transaction_mode?: string;\n  security_type?: string;\n  exchange_name?: string;\n  buy_value?: number;\n  sell_value?: number;\n  buy_quantity?: number;\n  sell_quantity?: number;\n  security_value?: number;\n  securities_acquired?: number;\n  shares_before_transaction?: number;\n  percentage_before_transaction?: number;\n  shares_after_transaction?: number;\n  percentage_after_transaction?: number;\n  remarks?: string;\n  xbrl_link?: string;\n  created_at: string;\n}\n\nexport interface TransactionListResponse {\n  transactions: Transaction[];\n  total_count: number;\n  page: number;\n  limit: number;\n  total_pages: number;\n}\n\nexport interface AnalyticsSummary {\n  total_transactions: number;\n  total_buy_value: number;\n  total_sell_value: number;\n  net_value: number;\n  unique_companies: number;\n  unique_persons: number;\n  date_range: {\n    earliest: string;\n    latest: string;\n  };\n}\n\nexport interface CompanyActivity {\n  symbol: string;\n  company_name: string;\n  transaction_count: number;\n  total_value: number;\n  total_buy_value: number;\n  total_sell_value: number;\n  net_value: number;\n  unique_insiders: number;\n  latest_transaction_date: string;\n}\n\nexport interface SearchResult {\n  type: string;\n  id?: string;\n  symbol?: string;\n  name: string;\n  category?: string;\n  transaction_count?: number;\n}\n\nexport interface SearchResponse {\n  results: SearchResult[];\n  total_count: number;\n  query: string;\n  search_type: string;\n}\n\nexport interface SystemStatus {\n  api_status: string;\n  database_status: {\n    total_transactions: number;\n    total_companies: number;\n    date_range?: {\n      earliest: string;\n      latest: string;\n    };\n    last_updated?: string;\n  };\n  scraper_status: {\n    last_execution?: string;\n    status: string;\n    records_fetched: number;\n    records_inserted: number;\n    records_skipped: number;\n    error_message?: string;\n  };\n  timestamp: string;\n}\n\nexport interface ManualScrapeResponse {\n  status: 'success' | 'error';\n  message: string;\n  records_fetched?: number;\n  records_inserted?: number;\n  records_skipped?: number;\n  execution_time?: number;\n  timestamp: string;\n}\n\n// API service class\nclass ApiService {\n  // Health check\n  async healthCheck(): Promise<AxiosResponse<any>> {\n    return api.get('/health');\n  }\n\n  // Transactions\n  async getTransactions(filters: TransactionFilters = {}): Promise<AxiosResponse<TransactionListResponse>> {\n    const params = new URLSearchParams();\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return api.get(`/transactions?${params.toString()}`);\n  }\n\n  // Analytics\n  async getAnalyticsSummary(fromDate?: string, toDate?: string): Promise<AxiosResponse<AnalyticsSummary>> {\n    const params = new URLSearchParams();\n    if (fromDate) params.append('from_date', fromDate);\n    if (toDate) params.append('to_date', toDate);\n    \n    return api.get(`/analytics/summary?${params.toString()}`);\n  }\n\n  async getTopCompanies(options: {\n    limit?: number;\n    sort_by?: string;\n    from_date?: string;\n    to_date?: string;\n  } = {}): Promise<AxiosResponse<CompanyActivity[]>> {\n    const params = new URLSearchParams();\n    \n    Object.entries(options).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return api.get(`/analytics/top-companies?${params.toString()}`);\n  }\n\n  // Companies\n  async getCompanyTransactions(\n    symbol: string,\n    options: {\n      from_date?: string;\n      to_date?: string;\n      page?: number;\n      limit?: number;\n    } = {}\n  ): Promise<AxiosResponse<TransactionListResponse>> {\n    const params = new URLSearchParams();\n    \n    Object.entries(options).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return api.get(`/companies/${symbol}/transactions?${params.toString()}`);\n  }\n\n  // Search\n  async search(\n    query: string,\n    type: 'all' | 'company' | 'person' = 'all',\n    limit: number = 10\n  ): Promise<AxiosResponse<SearchResponse>> {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      limit: limit.toString(),\n    });\n\n    return api.get(`/search?${params.toString()}`);\n  }\n\n  // System status\n  async getSystemStatus(): Promise<AxiosResponse<SystemStatus>> {\n    return api.get('/system/status');\n  }\n\n  // Manual scraping\n  async triggerManualScrape(daysBack: number = 7): Promise<AxiosResponse<ManualScrapeResponse>> {\n    return api.post(`/system/scrape?days_back=${daysBack}`);\n  }\n\n  // Utility method to format API errors\n  formatError(error: any): string {\n    if (error.response?.data?.detail) {\n      return error.response.data.detail;\n    } else if (error.response?.data?.message) {\n      return error.response.data.message;\n    } else if (error.message) {\n      return error.message;\n    } else {\n      return 'An unexpected error occurred';\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAyB,OAAO,CAE5C;AACA,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAE7E;AACA,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,KAAAC,cAAA,CACVC,OAAO,CAACC,GAAG,iBAAAC,MAAA,EAAAH,cAAA,CAAiBD,MAAM,CAACK,MAAM,UAAAJ,cAAA,iBAAbA,cAAA,CAAeK,WAAW,CAAC,CAAC,MAAAF,MAAA,CAAIJ,MAAM,CAACO,GAAG,CAAE,CAAC,CACzE,MAAO,CAAAP,MAAM,CACf,CAAC,CACAQ,KAAK,EAAK,CACTN,OAAO,CAACM,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAhB,GAAG,CAACK,YAAY,CAACc,QAAQ,CAACZ,GAAG,CAC1BY,QAAQ,EAAK,CACZ,MAAO,CAAAA,QAAQ,CACjB,CAAC,CACAH,KAAK,EAAK,CACTN,OAAO,CAACM,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAE3C,GAAIA,KAAK,CAACG,QAAQ,CAAE,CAClB;AACA,KAAM,CAAEC,MAAM,CAAEC,IAAK,CAAC,CAAGL,KAAK,CAACG,QAAQ,CACvCT,OAAO,CAACM,KAAK,cAAAJ,MAAA,CAAcQ,MAAM,MAAKC,IAAI,CAAC,CAC7C,CAAC,IAAM,IAAIL,KAAK,CAACV,OAAO,CAAE,CACxB;AACAI,OAAO,CAACM,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAACV,OAAO,CAAC,CACvD,CAAC,IAAM,CACL;AACAI,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAACM,OAAO,CAAC,CACtD,CAEA,MAAO,CAAAL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AA4HA;AACA,KAAM,CAAAO,UAAW,CACf;AACA,KAAM,CAAAC,WAAWA,CAAA,CAAgC,CAC/C,MAAO,CAAAxB,GAAG,CAACyB,GAAG,CAAC,SAAS,CAAC,CAC3B,CAEA;AACA,KAAM,CAAAC,eAAeA,CAAA,CAAoF,IAAnF,CAAAC,OAA2B,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACpD,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAEpCC,MAAM,CAACC,OAAO,CAACP,OAAO,CAAC,CAACQ,OAAO,CAACC,IAAA,EAAkB,IAAjB,CAACC,GAAG,CAAEC,KAAK,CAAC,CAAAF,IAAA,CAC3C,GAAIE,KAAK,GAAKR,SAAS,EAAIQ,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,EAAE,CAAE,CACzDP,MAAM,CAACQ,MAAM,CAACF,GAAG,CAAEC,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,MAAO,CAAAxC,GAAG,CAACyB,GAAG,kBAAAb,MAAA,CAAkBmB,MAAM,CAACS,QAAQ,CAAC,CAAC,CAAE,CAAC,CACtD,CAEA;AACA,KAAM,CAAAC,mBAAmBA,CAACC,QAAiB,CAAEC,MAAe,CAA4C,CACtG,KAAM,CAAAZ,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACpC,GAAIU,QAAQ,CAAEX,MAAM,CAACQ,MAAM,CAAC,WAAW,CAAEG,QAAQ,CAAC,CAClD,GAAIC,MAAM,CAAEZ,MAAM,CAACQ,MAAM,CAAC,SAAS,CAAEI,MAAM,CAAC,CAE5C,MAAO,CAAA3C,GAAG,CAACyB,GAAG,uBAAAb,MAAA,CAAuBmB,MAAM,CAACS,QAAQ,CAAC,CAAC,CAAE,CAAC,CAC3D,CAEA,KAAM,CAAAI,eAAeA,CAAA,CAK8B,IAL7B,CAAAC,OAKrB,CAAAjB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACJ,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAEpCC,MAAM,CAACC,OAAO,CAACW,OAAO,CAAC,CAACV,OAAO,CAACW,KAAA,EAAkB,IAAjB,CAACT,GAAG,CAAEC,KAAK,CAAC,CAAAQ,KAAA,CAC3C,GAAIR,KAAK,GAAKR,SAAS,EAAIQ,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,EAAE,CAAE,CACzDP,MAAM,CAACQ,MAAM,CAACF,GAAG,CAAEC,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,MAAO,CAAAxC,GAAG,CAACyB,GAAG,6BAAAb,MAAA,CAA6BmB,MAAM,CAACS,QAAQ,CAAC,CAAC,CAAE,CAAC,CACjE,CAEA;AACA,KAAM,CAAAO,sBAAsBA,CAC1BC,MAAc,CAOmC,IANjD,CAAAH,OAKC,CAAAjB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAEN,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAEpCC,MAAM,CAACC,OAAO,CAACW,OAAO,CAAC,CAACV,OAAO,CAACc,KAAA,EAAkB,IAAjB,CAACZ,GAAG,CAAEC,KAAK,CAAC,CAAAW,KAAA,CAC3C,GAAIX,KAAK,GAAKR,SAAS,EAAIQ,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,EAAE,CAAE,CACzDP,MAAM,CAACQ,MAAM,CAACF,GAAG,CAAEC,KAAK,CAACE,QAAQ,CAAC,CAAC,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,MAAO,CAAAxC,GAAG,CAACyB,GAAG,eAAAb,MAAA,CAAeoC,MAAM,mBAAApC,MAAA,CAAiBmB,MAAM,CAACS,QAAQ,CAAC,CAAC,CAAE,CAAC,CAC1E,CAEA;AACA,KAAM,CAAAU,MAAMA,CACVC,KAAa,CAG2B,IAFxC,CAAAC,IAAkC,CAAAxB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,IAC1C,CAAAyB,KAAa,CAAAzB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAElB,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CACjCsB,CAAC,CAAEH,KAAK,CACRC,IAAI,CACJC,KAAK,CAAEA,KAAK,CAACb,QAAQ,CAAC,CACxB,CAAC,CAAC,CAEF,MAAO,CAAAxC,GAAG,CAACyB,GAAG,YAAAb,MAAA,CAAYmB,MAAM,CAACS,QAAQ,CAAC,CAAC,CAAE,CAAC,CAChD,CAEA;AACA,KAAM,CAAAe,eAAeA,CAAA,CAAyC,CAC5D,MAAO,CAAAvD,GAAG,CAACyB,GAAG,CAAC,gBAAgB,CAAC,CAClC,CAEA;AACA,KAAM,CAAA+B,mBAAmBA,CAAA,CAAqE,IAApE,CAAAC,QAAgB,CAAA7B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC5C,MAAO,CAAA5B,GAAG,CAAC0D,IAAI,6BAAA9C,MAAA,CAA6B6C,QAAQ,CAAE,CAAC,CACzD,CAEA;AACAE,WAAWA,CAAC3C,KAAU,CAAU,KAAA4C,eAAA,CAAAC,oBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAC9B,IAAAH,eAAA,CAAI5C,KAAK,CAACG,QAAQ,UAAAyC,eAAA,YAAAC,oBAAA,CAAdD,eAAA,CAAgBvC,IAAI,UAAAwC,oBAAA,WAApBA,oBAAA,CAAsBG,MAAM,CAAE,CAChC,MAAO,CAAAhD,KAAK,CAACG,QAAQ,CAACE,IAAI,CAAC2C,MAAM,CACnC,CAAC,IAAM,KAAAF,gBAAA,CAAI9C,KAAK,CAACG,QAAQ,UAAA2C,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBzC,IAAI,UAAA0C,qBAAA,WAApBA,qBAAA,CAAsBzC,OAAO,CAAE,CACxC,MAAO,CAAAN,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACC,OAAO,CACpC,CAAC,IAAM,IAAIN,KAAK,CAACM,OAAO,CAAE,CACxB,MAAO,CAAAN,KAAK,CAACM,OAAO,CACtB,CAAC,IAAM,CACL,MAAO,8BAA8B,CACvC,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAA2C,UAAU,CAAG,GAAI,CAAA1C,UAAU,CAAC,CAAC,CAC1C,cAAe,CAAA0C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}