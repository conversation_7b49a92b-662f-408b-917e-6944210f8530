{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Chip,Tooltip,IconButton,CircularProgress}from'@mui/material';import{Refresh,CheckCircle,Warning,Error as ErrorIcon,Schedule}from'@mui/icons-material';import{formatDistanceToNow,format}from'date-fns';import{apiService}from'../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LastFetchedStatus=_ref=>{let{onRefresh,compact=false}=_ref;const[status,setStatus]=useState(null);const[loading,setLoading]=useState(false);const[scraping,setScraping]=useState(false);const[error,setError]=useState(null);const fetchStatus=async()=>{try{setLoading(true);setError(null);const response=await apiService.getSystemStatus();setStatus(response.data);}catch(err){console.error('Error fetching system status:',err);setError('Failed to fetch status');}finally{setLoading(false);}};useEffect(()=>{fetchStatus();// Refresh status every 30 seconds\nconst interval=setInterval(fetchStatus,30000);return()=>clearInterval(interval);},[]);const handleRefresh=async()=>{try{setScraping(true);setError(null);// Trigger manual scraping\nconsole.log('Triggering manual scrape...');const scrapeResponse=await apiService.triggerManualScrape(7);// Scrape last 7 days\nif(scrapeResponse.data.status==='success'){console.log('Manual scrape completed successfully:',scrapeResponse.data);// Wait a moment for the database to be updated, then refresh status\nsetTimeout(()=>{fetchStatus();},2000);if(onRefresh){onRefresh();}}else{const errorMessage=scrapeResponse.data.message||'Scraping failed';throw new Error(errorMessage);}}catch(err){console.error('Error during manual scrape:',err);setError(apiService.formatError(err));// Still try to refresh status even if scraping failed\nfetchStatus();if(onRefresh){onRefresh();}}finally{setScraping(false);}};const getStatusColor=lastUpdated=>{const now=new Date();const updated=new Date(lastUpdated);const diffMinutes=(now.getTime()-updated.getTime())/(1000*60);if(diffMinutes<30)return'success';if(diffMinutes<60)return'warning';return'error';};const getStatusIcon=lastUpdated=>{const color=getStatusColor(lastUpdated);switch(color){case'success':return/*#__PURE__*/_jsx(CheckCircle,{color:\"success\",fontSize:\"small\"});case'warning':return/*#__PURE__*/_jsx(Warning,{color:\"warning\",fontSize:\"small\"});case'error':return/*#__PURE__*/_jsx(ErrorIcon,{color:\"error\",fontSize:\"small\"});default:return/*#__PURE__*/_jsx(Schedule,{color:\"disabled\",fontSize:\"small\"});}};const formatLastUpdated=lastUpdated=>{try{const date=new Date(lastUpdated);const timeAgo=formatDistanceToNow(date,{addSuffix:true});const fullDate=format(date,'MMM dd, yyyy \\'at\\' h:mm a');return{timeAgo,fullDate};}catch(_unused){return{timeAgo:'Unknown',fullDate:'Unknown'};}};if((loading||scraping)&&!status){return/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(CircularProgress,{size:16}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:scraping?'Scraping new data...':'Loading status...'})]});}if(error||!status){return/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(ErrorIcon,{color:\"error\",fontSize:\"small\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:\"Status unavailable\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleRefresh,children:/*#__PURE__*/_jsx(Refresh,{fontSize:\"small\"})})]});}const lastUpdated=status.database_status.last_updated||status.timestamp;const{timeAgo,fullDate}=formatLastUpdated(lastUpdated);const statusColor=getStatusColor(lastUpdated);if(compact){return/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[scraping?/*#__PURE__*/_jsx(CircularProgress,{size:16}):getStatusIcon(lastUpdated),/*#__PURE__*/_jsx(Tooltip,{title:scraping?'Scraping new data...':\"Last updated: \".concat(fullDate),children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:scraping?'Scraping...':\"Updated \".concat(timeAgo)})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Scrape new data and refresh\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleRefresh,disabled:loading||scraping,children:/*#__PURE__*/_jsx(Refresh,{fontSize:\"small\"})})})]});}return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2,p:2,backgroundColor:'background.paper',borderRadius:1,border:1,borderColor:'divider'},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[scraping?/*#__PURE__*/_jsx(CircularProgress,{size:20}):getStatusIcon(lastUpdated),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:600,children:scraping?'Scraping Data...':'Data Status'}),/*#__PURE__*/_jsx(Tooltip,{title:scraping?'Scraping new data from NSE...':fullDate,children:/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:scraping?'Please wait...':\"Last updated \".concat(timeAgo)})})]})]}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(Chip,{label:\"\".concat(status.database_status.total_transactions,\" transactions\"),size:\"small\",variant:\"outlined\",color:\"primary\"}),/*#__PURE__*/_jsx(Chip,{label:\"\".concat(status.database_status.total_companies,\" companies\"),size:\"small\",variant:\"outlined\",color:\"secondary\"})]}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(Chip,{label:status.api_status==='healthy'?'API Online':'API Issues',size:\"small\",color:status.api_status==='healthy'?'success':'error'}),/*#__PURE__*/_jsx(Tooltip,{title:\"Scrape new data and refresh\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleRefresh,disabled:loading||scraping,children:/*#__PURE__*/_jsx(Refresh,{fontSize:\"small\"})})})]})]});};export default LastFetchedStatus;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "<PERSON><PERSON><PERSON>", "IconButton", "CircularProgress", "Refresh", "CheckCircle", "Warning", "Error", "ErrorIcon", "Schedule", "formatDistanceToNow", "format", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "LastFetchedStatus", "_ref", "onRefresh", "compact", "status", "setStatus", "loading", "setLoading", "scraping", "setScraping", "error", "setError", "fetchStatus", "response", "getSystemStatus", "data", "err", "console", "interval", "setInterval", "clearInterval", "handleRefresh", "log", "scrapeResponse", "triggerManualScrape", "setTimeout", "errorMessage", "message", "formatError", "getStatusColor", "lastUpdated", "now", "Date", "updated", "diffMinutes", "getTime", "getStatusIcon", "color", "fontSize", "formatLastUpdated", "date", "timeAgo", "addSuffix", "fullDate", "_unused", "display", "alignItems", "gap", "children", "size", "variant", "onClick", "database_status", "last_updated", "timestamp", "statusColor", "title", "concat", "disabled", "sx", "p", "backgroundColor", "borderRadius", "border", "borderColor", "fontWeight", "label", "total_transactions", "total_companies", "api_status"], "sources": ["D:/chirag/nsescrapper/frontend/src/components/LastFetchedStatus.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Chip,\n  Tooltip,\n  IconButton,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  Refresh,\n  CheckCircle,\n  Warning,\n  Error as ErrorIcon,\n  Schedule,\n} from '@mui/icons-material';\nimport { formatDistanceToNow, format } from 'date-fns';\nimport { apiService } from '../services/apiService';\n\ninterface LastFetchedStatusProps {\n  onRefresh?: () => void;\n  compact?: boolean;\n}\n\ninterface SystemStatus {\n  api_status: string;\n  database_status: {\n    total_transactions: number;\n    total_companies: number;\n    date_range?: {\n      earliest: string;\n      latest: string;\n    };\n    last_updated?: string;\n  };\n  scraper_status: {\n    last_execution?: string;\n    status: string;\n    records_fetched: number;\n    records_inserted: number;\n    records_skipped: number;\n    error_message?: string;\n  };\n  timestamp: string;\n}\n\nconst LastFetchedStatus: React.FC<LastFetchedStatusProps> = ({\n  onRefresh,\n  compact = false\n}) => {\n  const [status, setStatus] = useState<SystemStatus | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [scraping, setScraping] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStatus = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await apiService.getSystemStatus();\n      setStatus(response.data);\n    } catch (err) {\n      console.error('Error fetching system status:', err);\n      setError('Failed to fetch status');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchStatus();\n    // Refresh status every 30 seconds\n    const interval = setInterval(fetchStatus, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleRefresh = async () => {\n    try {\n      setScraping(true);\n      setError(null);\n\n      // Trigger manual scraping\n      console.log('Triggering manual scrape...');\n      const scrapeResponse = await apiService.triggerManualScrape(7); // Scrape last 7 days\n\n      if (scrapeResponse.data.status === 'success') {\n        console.log('Manual scrape completed successfully:', scrapeResponse.data);\n\n        // Wait a moment for the database to be updated, then refresh status\n        setTimeout(() => {\n          fetchStatus();\n        }, 2000);\n\n        if (onRefresh) {\n          onRefresh();\n        }\n      } else {\n        const errorMessage: string = scrapeResponse.data.message || 'Scraping failed';\n        throw new Error(errorMessage);\n      }\n    } catch (err: any) {\n      console.error('Error during manual scrape:', err);\n      setError(apiService.formatError(err));\n\n      // Still try to refresh status even if scraping failed\n      fetchStatus();\n      if (onRefresh) {\n        onRefresh();\n      }\n    } finally {\n      setScraping(false);\n    }\n  };\n\n  const getStatusColor = (lastUpdated: string) => {\n    const now = new Date();\n    const updated = new Date(lastUpdated);\n    const diffMinutes = (now.getTime() - updated.getTime()) / (1000 * 60);\n    \n    if (diffMinutes < 30) return 'success';\n    if (diffMinutes < 60) return 'warning';\n    return 'error';\n  };\n\n  const getStatusIcon = (lastUpdated: string) => {\n    const color = getStatusColor(lastUpdated);\n    switch (color) {\n      case 'success':\n        return <CheckCircle color=\"success\" fontSize=\"small\" />;\n      case 'warning':\n        return <Warning color=\"warning\" fontSize=\"small\" />;\n      case 'error':\n        return <ErrorIcon color=\"error\" fontSize=\"small\" />;\n      default:\n        return <Schedule color=\"disabled\" fontSize=\"small\" />;\n    }\n  };\n\n  const formatLastUpdated = (lastUpdated: string) => {\n    try {\n      const date = new Date(lastUpdated);\n      const timeAgo = formatDistanceToNow(date, { addSuffix: true });\n      const fullDate = format(date, 'MMM dd, yyyy \\'at\\' h:mm a');\n      return { timeAgo, fullDate };\n    } catch {\n      return { timeAgo: 'Unknown', fullDate: 'Unknown' };\n    }\n  };\n\n  if ((loading || scraping) && !status) {\n    return (\n      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n        <CircularProgress size={16} />\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {scraping ? 'Scraping new data...' : 'Loading status...'}\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error || !status) {\n    return (\n      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n        <ErrorIcon color=\"error\" fontSize=\"small\" />\n        <Typography variant=\"body2\" color=\"error\">\n          Status unavailable\n        </Typography>\n        <IconButton size=\"small\" onClick={handleRefresh}>\n          <Refresh fontSize=\"small\" />\n        </IconButton>\n      </Box>\n    );\n  }\n\n  const lastUpdated = status.database_status.last_updated || status.timestamp;\n  const { timeAgo, fullDate } = formatLastUpdated(lastUpdated);\n  const statusColor = getStatusColor(lastUpdated);\n\n  if (compact) {\n    return (\n      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n        {scraping ? <CircularProgress size={16} /> : getStatusIcon(lastUpdated)}\n        <Tooltip title={scraping ? 'Scraping new data...' : `Last updated: ${fullDate}`}>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            {scraping ? 'Scraping...' : `Updated ${timeAgo}`}\n          </Typography>\n        </Tooltip>\n        <Tooltip title=\"Scrape new data and refresh\">\n          <IconButton size=\"small\" onClick={handleRefresh} disabled={loading || scraping}>\n            <Refresh fontSize=\"small\" />\n          </IconButton>\n        </Tooltip>\n      </Box>\n    );\n  }\n\n  return (\n    <Box \n      sx={{ \n        display: 'flex', \n        alignItems: 'center', \n        gap: 2,\n        p: 2,\n        backgroundColor: 'background.paper',\n        borderRadius: 1,\n        border: 1,\n        borderColor: 'divider',\n      }}\n    >\n      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n        {scraping ? <CircularProgress size={20} /> : getStatusIcon(lastUpdated)}\n        <Box>\n          <Typography variant=\"body2\" fontWeight={600}>\n            {scraping ? 'Scraping Data...' : 'Data Status'}\n          </Typography>\n          <Tooltip title={scraping ? 'Scraping new data from NSE...' : fullDate}>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {scraping ? 'Please wait...' : `Last updated ${timeAgo}`}\n            </Typography>\n          </Tooltip>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n        <Chip\n          label={`${status.database_status.total_transactions} transactions`}\n          size=\"small\"\n          variant=\"outlined\"\n          color=\"primary\"\n        />\n        <Chip\n          label={`${status.database_status.total_companies} companies`}\n          size=\"small\"\n          variant=\"outlined\"\n          color=\"secondary\"\n        />\n      </Box>\n\n      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n        <Chip\n          label={status.api_status === 'healthy' ? 'API Online' : 'API Issues'}\n          size=\"small\"\n          color={status.api_status === 'healthy' ? 'success' : 'error'}\n        />\n        <Tooltip title=\"Scrape new data and refresh\">\n          <IconButton size=\"small\" onClick={handleRefresh} disabled={loading || scraping}>\n            <Refresh fontSize=\"small\" />\n          </IconButton>\n        </Tooltip>\n      </Box>\n    </Box>\n  );\n};\n\nexport default LastFetchedStatus;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,OAAO,CACPC,UAAU,CACVC,gBAAgB,KACX,eAAe,CACtB,OACEC,OAAO,CACPC,WAAW,CACXC,OAAO,CACPC,KAAK,GAAI,CAAAC,SAAS,CAClBC,QAAQ,KACH,qBAAqB,CAC5B,OAASC,mBAAmB,CAAEC,MAAM,KAAQ,UAAU,CACtD,OAASC,UAAU,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA6BpD,KAAM,CAAAC,iBAAmD,CAAGC,IAAA,EAGtD,IAHuD,CAC3DC,SAAS,CACTC,OAAO,CAAG,KACZ,CAAC,CAAAF,IAAA,CACC,KAAM,CAACG,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAsB,IAAI,CAAC,CAC/D,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC+B,KAAK,CAAEC,QAAQ,CAAC,CAAGhC,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAAAiC,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACFL,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAACmB,eAAe,CAAC,CAAC,CACnDT,SAAS,CAACQ,QAAQ,CAACE,IAAI,CAAC,CAC1B,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACP,KAAK,CAAC,+BAA+B,CAAEM,GAAG,CAAC,CACnDL,QAAQ,CAAC,wBAAwB,CAAC,CACpC,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED3B,SAAS,CAAC,IAAM,CACdgC,WAAW,CAAC,CAAC,CACb;AACA,KAAM,CAAAM,QAAQ,CAAGC,WAAW,CAACP,WAAW,CAAE,KAAK,CAAC,CAChD,MAAO,IAAMQ,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFZ,WAAW,CAAC,IAAI,CAAC,CACjBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACAM,OAAO,CAACK,GAAG,CAAC,6BAA6B,CAAC,CAC1C,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAA5B,UAAU,CAAC6B,mBAAmB,CAAC,CAAC,CAAC,CAAE;AAEhE,GAAID,cAAc,CAACR,IAAI,CAACX,MAAM,GAAK,SAAS,CAAE,CAC5Ca,OAAO,CAACK,GAAG,CAAC,uCAAuC,CAAEC,cAAc,CAACR,IAAI,CAAC,CAEzE;AACAU,UAAU,CAAC,IAAM,CACfb,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,IAAI,CAAC,CAER,GAAIV,SAAS,CAAE,CACbA,SAAS,CAAC,CAAC,CACb,CACF,CAAC,IAAM,CACL,KAAM,CAAAwB,YAAoB,CAAGH,cAAc,CAACR,IAAI,CAACY,OAAO,EAAI,iBAAiB,CAC7E,KAAM,IAAI,CAAArC,KAAK,CAACoC,YAAY,CAAC,CAC/B,CACF,CAAE,MAAOV,GAAQ,CAAE,CACjBC,OAAO,CAACP,KAAK,CAAC,6BAA6B,CAAEM,GAAG,CAAC,CACjDL,QAAQ,CAAChB,UAAU,CAACiC,WAAW,CAACZ,GAAG,CAAC,CAAC,CAErC;AACAJ,WAAW,CAAC,CAAC,CACb,GAAIV,SAAS,CAAE,CACbA,SAAS,CAAC,CAAC,CACb,CACF,CAAC,OAAS,CACRO,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAoB,cAAc,CAAIC,WAAmB,EAAK,CAC9C,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAD,IAAI,CAACF,WAAW,CAAC,CACrC,KAAM,CAAAI,WAAW,CAAG,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAGF,OAAO,CAACE,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAC,CAErE,GAAID,WAAW,CAAG,EAAE,CAAE,MAAO,SAAS,CACtC,GAAIA,WAAW,CAAG,EAAE,CAAE,MAAO,SAAS,CACtC,MAAO,OAAO,CAChB,CAAC,CAED,KAAM,CAAAE,aAAa,CAAIN,WAAmB,EAAK,CAC7C,KAAM,CAAAO,KAAK,CAAGR,cAAc,CAACC,WAAW,CAAC,CACzC,OAAQO,KAAK,EACX,IAAK,SAAS,CACZ,mBAAOxC,IAAA,CAACT,WAAW,EAACiD,KAAK,CAAC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAE,CAAC,CACzD,IAAK,SAAS,CACZ,mBAAOzC,IAAA,CAACR,OAAO,EAACgD,KAAK,CAAC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrD,IAAK,OAAO,CACV,mBAAOzC,IAAA,CAACN,SAAS,EAAC8C,KAAK,CAAC,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrD,QACE,mBAAOzC,IAAA,CAACL,QAAQ,EAAC6C,KAAK,CAAC,UAAU,CAACC,QAAQ,CAAC,OAAO,CAAE,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIT,WAAmB,EAAK,CACjD,GAAI,CACF,KAAM,CAAAU,IAAI,CAAG,GAAI,CAAAR,IAAI,CAACF,WAAW,CAAC,CAClC,KAAM,CAAAW,OAAO,CAAGhD,mBAAmB,CAAC+C,IAAI,CAAE,CAAEE,SAAS,CAAE,IAAK,CAAC,CAAC,CAC9D,KAAM,CAAAC,QAAQ,CAAGjD,MAAM,CAAC8C,IAAI,CAAE,4BAA4B,CAAC,CAC3D,MAAO,CAAEC,OAAO,CAAEE,QAAS,CAAC,CAC9B,CAAE,MAAAC,OAAA,CAAM,CACN,MAAO,CAAEH,OAAO,CAAE,SAAS,CAAEE,QAAQ,CAAE,SAAU,CAAC,CACpD,CACF,CAAC,CAED,GAAI,CAACrC,OAAO,EAAIE,QAAQ,GAAK,CAACJ,MAAM,CAAE,CACpC,mBACEL,KAAA,CAAClB,GAAG,EAACgE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAAC,QAAA,eAC7CnD,IAAA,CAACX,gBAAgB,EAAC+D,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BpD,IAAA,CAACf,UAAU,EAACoE,OAAO,CAAC,OAAO,CAACb,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CAC/CxC,QAAQ,CAAG,sBAAsB,CAAG,mBAAmB,CAC9C,CAAC,EACV,CAAC,CAEV,CAEA,GAAIE,KAAK,EAAI,CAACN,MAAM,CAAE,CACpB,mBACEL,KAAA,CAAClB,GAAG,EAACgE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAAC,QAAA,eAC7CnD,IAAA,CAACN,SAAS,EAAC8C,KAAK,CAAC,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAE,CAAC,cAC5CzC,IAAA,CAACf,UAAU,EAACoE,OAAO,CAAC,OAAO,CAACb,KAAK,CAAC,OAAO,CAAAW,QAAA,CAAC,oBAE1C,CAAY,CAAC,cACbnD,IAAA,CAACZ,UAAU,EAACgE,IAAI,CAAC,OAAO,CAACE,OAAO,CAAE9B,aAAc,CAAA2B,QAAA,cAC9CnD,IAAA,CAACV,OAAO,EAACmD,QAAQ,CAAC,OAAO,CAAE,CAAC,CAClB,CAAC,EACV,CAAC,CAEV,CAEA,KAAM,CAAAR,WAAW,CAAG1B,MAAM,CAACgD,eAAe,CAACC,YAAY,EAAIjD,MAAM,CAACkD,SAAS,CAC3E,KAAM,CAAEb,OAAO,CAAEE,QAAS,CAAC,CAAGJ,iBAAiB,CAACT,WAAW,CAAC,CAC5D,KAAM,CAAAyB,WAAW,CAAG1B,cAAc,CAACC,WAAW,CAAC,CAE/C,GAAI3B,OAAO,CAAE,CACX,mBACEJ,KAAA,CAAClB,GAAG,EAACgE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAAC,QAAA,EAC5CxC,QAAQ,cAAGX,IAAA,CAACX,gBAAgB,EAAC+D,IAAI,CAAE,EAAG,CAAE,CAAC,CAAGb,aAAa,CAACN,WAAW,CAAC,cACvEjC,IAAA,CAACb,OAAO,EAACwE,KAAK,CAAEhD,QAAQ,CAAG,sBAAsB,kBAAAiD,MAAA,CAAoBd,QAAQ,CAAG,CAAAK,QAAA,cAC9EnD,IAAA,CAACf,UAAU,EAACoE,OAAO,CAAC,OAAO,CAACb,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CAC/CxC,QAAQ,CAAG,aAAa,YAAAiD,MAAA,CAAchB,OAAO,CAAE,CACtC,CAAC,CACN,CAAC,cACV5C,IAAA,CAACb,OAAO,EAACwE,KAAK,CAAC,6BAA6B,CAAAR,QAAA,cAC1CnD,IAAA,CAACZ,UAAU,EAACgE,IAAI,CAAC,OAAO,CAACE,OAAO,CAAE9B,aAAc,CAACqC,QAAQ,CAAEpD,OAAO,EAAIE,QAAS,CAAAwC,QAAA,cAC7EnD,IAAA,CAACV,OAAO,EAACmD,QAAQ,CAAC,OAAO,CAAE,CAAC,CAClB,CAAC,CACN,CAAC,EACP,CAAC,CAEV,CAEA,mBACEvC,KAAA,CAAClB,GAAG,EACF8E,EAAE,CAAE,CACFd,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CAAC,CACNa,CAAC,CAAE,CAAC,CACJC,eAAe,CAAE,kBAAkB,CACnCC,YAAY,CAAE,CAAC,CACfC,MAAM,CAAE,CAAC,CACTC,WAAW,CAAE,SACf,CAAE,CAAAhB,QAAA,eAEFjD,KAAA,CAAClB,GAAG,EAACgE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAAC,QAAA,EAC5CxC,QAAQ,cAAGX,IAAA,CAACX,gBAAgB,EAAC+D,IAAI,CAAE,EAAG,CAAE,CAAC,CAAGb,aAAa,CAACN,WAAW,CAAC,cACvE/B,KAAA,CAAClB,GAAG,EAAAmE,QAAA,eACFnD,IAAA,CAACf,UAAU,EAACoE,OAAO,CAAC,OAAO,CAACe,UAAU,CAAE,GAAI,CAAAjB,QAAA,CACzCxC,QAAQ,CAAG,kBAAkB,CAAG,aAAa,CACpC,CAAC,cACbX,IAAA,CAACb,OAAO,EAACwE,KAAK,CAAEhD,QAAQ,CAAG,+BAA+B,CAAGmC,QAAS,CAAAK,QAAA,cACpEnD,IAAA,CAACf,UAAU,EAACoE,OAAO,CAAC,SAAS,CAACb,KAAK,CAAC,gBAAgB,CAAAW,QAAA,CACjDxC,QAAQ,CAAG,gBAAgB,iBAAAiD,MAAA,CAAmBhB,OAAO,CAAE,CAC9C,CAAC,CACN,CAAC,EACP,CAAC,EACH,CAAC,cAEN1C,KAAA,CAAClB,GAAG,EAACgE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAAC,QAAA,eAC7CnD,IAAA,CAACd,IAAI,EACHmF,KAAK,IAAAT,MAAA,CAAKrD,MAAM,CAACgD,eAAe,CAACe,kBAAkB,iBAAgB,CACnElB,IAAI,CAAC,OAAO,CACZC,OAAO,CAAC,UAAU,CAClBb,KAAK,CAAC,SAAS,CAChB,CAAC,cACFxC,IAAA,CAACd,IAAI,EACHmF,KAAK,IAAAT,MAAA,CAAKrD,MAAM,CAACgD,eAAe,CAACgB,eAAe,cAAa,CAC7DnB,IAAI,CAAC,OAAO,CACZC,OAAO,CAAC,UAAU,CAClBb,KAAK,CAAC,WAAW,CAClB,CAAC,EACC,CAAC,cAENtC,KAAA,CAAClB,GAAG,EAACgE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAAC,QAAA,eAC7CnD,IAAA,CAACd,IAAI,EACHmF,KAAK,CAAE9D,MAAM,CAACiE,UAAU,GAAK,SAAS,CAAG,YAAY,CAAG,YAAa,CACrEpB,IAAI,CAAC,OAAO,CACZZ,KAAK,CAAEjC,MAAM,CAACiE,UAAU,GAAK,SAAS,CAAG,SAAS,CAAG,OAAQ,CAC9D,CAAC,cACFxE,IAAA,CAACb,OAAO,EAACwE,KAAK,CAAC,6BAA6B,CAAAR,QAAA,cAC1CnD,IAAA,CAACZ,UAAU,EAACgE,IAAI,CAAC,OAAO,CAACE,OAAO,CAAE9B,aAAc,CAACqC,QAAQ,CAAEpD,OAAO,EAAIE,QAAS,CAAAwC,QAAA,cAC7EnD,IAAA,CAACV,OAAO,EAACmD,QAAQ,CAAC,OAAO,CAAE,CAAC,CAClB,CAAC,CACN,CAAC,EACP,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}