{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Card,CardContent,Grid,TextField,List,ListItem,ListItemText,ListItemSecondaryAction,Chip,Alert,CircularProgress,InputAdornment}from'@mui/material';import{Search,Business}from'@mui/icons-material';import{apiService}from'../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Companies=()=>{const[companies,setCompanies]=useState([]);const[filteredCompanies,setFilteredCompanies]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[searchQuery,setSearchQuery]=useState('');useEffect(()=>{const fetchCompanies=async()=>{try{setLoading(true);setError(null);const response=await apiService.getTopCompanies({limit:100});setCompanies(response.data);setFilteredCompanies(response.data);}catch(err){console.error('Error fetching companies:',err);setError('Failed to load companies data');}finally{setLoading(false);}};fetchCompanies();},[]);useEffect(()=>{if(!searchQuery){setFilteredCompanies(companies);}else{const filtered=companies.filter(company=>company.symbol.toLowerCase().includes(searchQuery.toLowerCase())||company.company_name.toLowerCase().includes(searchQuery.toLowerCase()));setFilteredCompanies(filtered);}},[searchQuery,companies]);const formatCurrency=value=>{if(value===null||value===undefined||isNaN(value)){return'₹0';}const absValue=Math.abs(value);if(absValue>=10000000){return\"\\u20B9\".concat((value/10000000).toFixed(1),\"Cr\");}else if(absValue>=100000){return\"\\u20B9\".concat((value/100000).toFixed(1),\"L\");}else{return\"\\u20B9\".concat(value.toLocaleString());}};const formatDate=dateString=>{try{return new Date(dateString).toLocaleDateString();}catch(_unused){return dateString;}};if(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{size:60})});}if(error){return/*#__PURE__*/_jsx(Box,{p:3,children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error})});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{mb:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Companies\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"Browse companies with insider trading activity\"})]}),/*#__PURE__*/_jsx(Card,{sx:{mb:3},children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"Search companies by symbol or name...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Search,{})})}})})}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:4},children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,children:[/*#__PURE__*/_jsx(Business,{color:\"primary\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"primary.main\",children:companies.length}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Companies\"})]})]})})})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:4},children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,children:[/*#__PURE__*/_jsx(Business,{color:\"success\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"success.main\",children:companies.reduce((sum,c)=>sum+c.transaction_count,0).toLocaleString()}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Transactions\"})]})]})})})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:4},children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,children:[/*#__PURE__*/_jsx(Business,{color:\"info\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",color:\"info.main\",children:formatCurrency(companies.reduce((sum,c)=>sum+c.total_value,0))}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Total Value\"})]})]})})})})]}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Companies (\",filteredCompanies.length,\")\"]}),filteredCompanies.length===0?/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"200px\",children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:searchQuery?'No companies found matching your search':'No companies data available'})}):/*#__PURE__*/_jsx(List,{children:filteredCompanies.map((company,index)=>/*#__PURE__*/_jsxs(ListItem,{divider:index<filteredCompanies.length-1,sx:{'&:hover':{backgroundColor:'action.hover'}},children:[/*#__PURE__*/_jsx(ListItemText,{primary:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:600,children:company.symbol}),/*#__PURE__*/_jsx(Chip,{label:\"\".concat(company.transaction_count,\" transactions\"),size:\"small\",variant:\"outlined\",color:\"primary\"})]}),secondary:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.primary\",children:company.company_name}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[company.unique_insiders,\" unique insiders \\u2022 Last activity: \",formatDate(company.latest_transaction_date)]})]})}),/*#__PURE__*/_jsx(ListItemSecondaryAction,{children:/*#__PURE__*/_jsxs(Box,{textAlign:\"right\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:600,color:\"primary.main\",children:formatCurrency(company.total_value)}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"success.main\",children:[\"Buy: \",formatCurrency(company.total_buy_value)]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"error.main\",children:[\"Sell: \",formatCurrency(company.total_sell_value)]})]})})]},company.symbol))})]})})]});};export default Companies;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "TextField", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Chip", "<PERSON><PERSON>", "CircularProgress", "InputAdornment", "Search", "Business", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "Companies", "companies", "setCompanies", "filteredCompanies", "setFilteredCompanies", "loading", "setLoading", "error", "setError", "searchQuery", "setSearch<PERSON>uery", "fetchCompanies", "response", "getTopCompanies", "limit", "data", "err", "console", "filtered", "filter", "company", "symbol", "toLowerCase", "includes", "company_name", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "concat", "toFixed", "toLocaleString", "formatDate", "dateString", "Date", "toLocaleDateString", "_unused", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "p", "severity", "mb", "variant", "gutterBottom", "color", "sx", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "container", "spacing", "xs", "sm", "gap", "length", "reduce", "sum", "c", "transaction_count", "total_value", "map", "index", "divider", "backgroundColor", "primary", "fontWeight", "label", "secondary", "unique_insiders", "latest_transaction_date", "textAlign", "total_buy_value", "total_sell_value"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Companies.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  TextField,\n  Button,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Chip,\n  Alert,\n  CircularProgress,\n  InputAdornment,\n} from '@mui/material';\nimport { Search, Business } from '@mui/icons-material';\nimport { apiService, CompanyActivity } from '../services/apiService';\n\nconst Companies: React.FC = () => {\n  const [companies, setCompanies] = useState<CompanyActivity[]>([]);\n  const [filteredCompanies, setFilteredCompanies] = useState<CompanyActivity[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  useEffect(() => {\n    const fetchCompanies = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const response = await apiService.getTopCompanies({ limit: 100 });\n        setCompanies(response.data);\n        setFilteredCompanies(response.data);\n      } catch (err) {\n        console.error('Error fetching companies:', err);\n        setError('Failed to load companies data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCompanies();\n  }, []);\n\n  useEffect(() => {\n    if (!searchQuery) {\n      setFilteredCompanies(companies);\n    } else {\n      const filtered = companies.filter(\n        (company) =>\n          company.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||\n          company.company_name.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredCompanies(filtered);\n    }\n  }, [searchQuery, companies]);\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    try {\n      return new Date(dateString).toLocaleDateString();\n    } catch {\n      return dateString;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Companies\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Browse companies with insider trading activity\n        </Typography>\n      </Box>\n\n      {/* Search */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <TextField\n            fullWidth\n            placeholder=\"Search companies by symbol or name...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <Search />\n                </InputAdornment>\n              ),\n            }}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Summary Stats */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid size={{ xs: 12, sm: 4 }}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"primary\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"primary.main\">\n                    {companies.length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Companies\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid size={{ xs: 12, sm: 4 }}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"success\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"success.main\">\n                    {companies.reduce((sum, c) => sum + c.transaction_count, 0).toLocaleString()}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Transactions\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid size={{ xs: 12, sm: 4 }}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"info\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"info.main\">\n                    {formatCurrency(companies.reduce((sum, c) => sum + c.total_value, 0))}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Value\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Companies List */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Companies ({filteredCompanies.length})\n          </Typography>\n          \n          {filteredCompanies.length === 0 ? (\n            <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                {searchQuery ? 'No companies found matching your search' : 'No companies data available'}\n              </Typography>\n            </Box>\n          ) : (\n            <List>\n              {filteredCompanies.map((company, index) => (\n                <ListItem\n                  key={company.symbol}\n                  divider={index < filteredCompanies.length - 1}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'action.hover',\n                    },\n                  }}\n                >\n                  <ListItemText\n                    primary={\n                      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                        <Typography variant=\"subtitle1\" fontWeight={600}>\n                          {company.symbol}\n                        </Typography>\n                        <Chip\n                          label={`${company.transaction_count} transactions`}\n                          size=\"small\"\n                          variant=\"outlined\"\n                          color=\"primary\"\n                        />\n                      </Box>\n                    }\n                    secondary={\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.primary\">\n                          {company.company_name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {company.unique_insiders} unique insiders • \n                          Last activity: {formatDate(company.latest_transaction_date)}\n                        </Typography>\n                      </Box>\n                    }\n                  />\n                  <ListItemSecondaryAction>\n                    <Box textAlign=\"right\">\n                      <Typography variant=\"subtitle1\" fontWeight={600} color=\"primary.main\">\n                        {formatCurrency(company.total_value)}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"success.main\">\n                        Buy: {formatCurrency(company.total_buy_value)}\n                      </Typography>\n                      <br />\n                      <Typography variant=\"caption\" color=\"error.main\">\n                        Sell: {formatCurrency(company.total_sell_value)}\n                      </Typography>\n                    </Box>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n            </List>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default Companies;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,WAAW,CACXC,IAAI,CACJC,SAAS,CAETC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,uBAAuB,CACvBC,IAAI,CACJC,KAAK,CACLC,gBAAgB,CAChBC,cAAc,KACT,eAAe,CACtB,OAASC,MAAM,CAAEC,QAAQ,KAAQ,qBAAqB,CACtD,OAASC,UAAU,KAAyB,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErE,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGzB,QAAQ,CAAoB,EAAE,CAAC,CACjE,KAAM,CAAC0B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3B,QAAQ,CAAoB,EAAE,CAAC,CACjF,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,KAAK,CAAEC,QAAQ,CAAC,CAAG/B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAElDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiC,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACFL,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAjB,UAAU,CAACkB,eAAe,CAAC,CAAEC,KAAK,CAAE,GAAI,CAAC,CAAC,CACjEZ,YAAY,CAACU,QAAQ,CAACG,IAAI,CAAC,CAC3BX,oBAAoB,CAACQ,QAAQ,CAACG,IAAI,CAAC,CACrC,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,CAAES,GAAG,CAAC,CAC/CR,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDK,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAENjC,SAAS,CAAC,IAAM,CACd,GAAI,CAAC+B,WAAW,CAAE,CAChBL,oBAAoB,CAACH,SAAS,CAAC,CACjC,CAAC,IAAM,CACL,KAAM,CAAAiB,QAAQ,CAAGjB,SAAS,CAACkB,MAAM,CAC9BC,OAAO,EACNA,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,WAAW,CAACa,WAAW,CAAC,CAAC,CAAC,EAChEF,OAAO,CAACI,YAAY,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,WAAW,CAACa,WAAW,CAAC,CAAC,CACzE,CAAC,CACDlB,oBAAoB,CAACc,QAAQ,CAAC,CAChC,CACF,CAAC,CAAE,CAACT,WAAW,CAAER,SAAS,CAAC,CAAC,CAE5B,KAAM,CAAAwB,cAAc,CAAIC,KAAgC,EAAK,CAC3D,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAIC,KAAK,CAACF,KAAK,CAAC,CAAE,CACzD,MAAO,IAAI,CACb,CAEA,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,CAChC,GAAIG,QAAQ,EAAI,QAAQ,CAAE,CACxB,eAAAG,MAAA,CAAW,CAACN,KAAK,CAAG,QAAQ,EAAEO,OAAO,CAAC,CAAC,CAAC,OAC1C,CAAC,IAAM,IAAIJ,QAAQ,EAAI,MAAM,CAAE,CAC7B,eAAAG,MAAA,CAAW,CAACN,KAAK,CAAG,MAAM,EAAEO,OAAO,CAAC,CAAC,CAAC,MACxC,CAAC,IAAM,CACL,eAAAD,MAAA,CAAWN,KAAK,CAACQ,cAAc,CAAC,CAAC,EACnC,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAkB,EAAK,CACzC,GAAI,CACF,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAClD,CAAE,MAAAC,OAAA,CAAM,CACN,MAAO,CAAAH,UAAU,CACnB,CACF,CAAC,CAED,GAAI/B,OAAO,CAAE,CACX,mBACER,IAAA,CAAClB,GAAG,EAAC6D,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/E/C,IAAA,CAACN,gBAAgB,EAACsD,IAAI,CAAE,EAAG,CAAE,CAAC,CAC3B,CAAC,CAEV,CAEA,GAAItC,KAAK,CAAE,CACT,mBACEV,IAAA,CAAClB,GAAG,EAACmE,CAAC,CAAE,CAAE,CAAAF,QAAA,cACR/C,IAAA,CAACP,KAAK,EAACyD,QAAQ,CAAC,OAAO,CAAAH,QAAA,CAAErC,KAAK,CAAQ,CAAC,CACpC,CAAC,CAEV,CAEA,mBACER,KAAA,CAACpB,GAAG,EAAAiE,QAAA,eAEF7C,KAAA,CAACpB,GAAG,EAACqE,EAAE,CAAE,CAAE,CAAAJ,QAAA,eACT/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAN,QAAA,CAAC,WAEtC,CAAY,CAAC,cACb/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAAC,gDAEnD,CAAY,CAAC,EACV,CAAC,cAGN/C,IAAA,CAAChB,IAAI,EAACuE,EAAE,CAAE,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAClB/C,IAAA,CAACf,WAAW,EAAA8D,QAAA,cACV/C,IAAA,CAACb,SAAS,EACRqE,SAAS,MACTC,WAAW,CAAC,uCAAuC,CACnD5B,KAAK,CAAEjB,WAAY,CACnB8C,QAAQ,CAAGC,CAAC,EAAK9C,cAAc,CAAC8C,CAAC,CAACC,MAAM,CAAC/B,KAAK,CAAE,CAChDgC,UAAU,CAAE,CACVC,cAAc,cACZ9D,IAAA,CAACL,cAAc,EAACoE,QAAQ,CAAC,OAAO,CAAAhB,QAAA,cAC9B/C,IAAA,CAACJ,MAAM,GAAE,CAAC,CACI,CAEpB,CAAE,CACH,CAAC,CACS,CAAC,CACV,CAAC,cAGPM,KAAA,CAAChB,IAAI,EAAC8E,SAAS,MAACC,OAAO,CAAE,CAAE,CAACV,EAAE,CAAE,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACxC/C,IAAA,CAACd,IAAI,EAAC8D,IAAI,CAAE,CAAEkB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAApB,QAAA,cAC5B/C,IAAA,CAAChB,IAAI,EAAA+D,QAAA,cACH/C,IAAA,CAACf,WAAW,EAAA8D,QAAA,cACV7C,KAAA,CAACpB,GAAG,EAAC6D,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACuB,GAAG,CAAE,CAAE,CAAArB,QAAA,eAC7C/C,IAAA,CAACH,QAAQ,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,cAC5BpD,KAAA,CAACpB,GAAG,EAAAiE,QAAA,eACF/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC,cAAc,CAAAP,QAAA,CAC1C3C,SAAS,CAACiE,MAAM,CACP,CAAC,cACbrE,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAAC,iBAEnD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,CACK,CAAC,CACV,CAAC,CACH,CAAC,cACP/C,IAAA,CAACd,IAAI,EAAC8D,IAAI,CAAE,CAAEkB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAApB,QAAA,cAC5B/C,IAAA,CAAChB,IAAI,EAAA+D,QAAA,cACH/C,IAAA,CAACf,WAAW,EAAA8D,QAAA,cACV7C,KAAA,CAACpB,GAAG,EAAC6D,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACuB,GAAG,CAAE,CAAE,CAAArB,QAAA,eAC7C/C,IAAA,CAACH,QAAQ,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,cAC5BpD,KAAA,CAACpB,GAAG,EAAAiE,QAAA,eACF/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC,cAAc,CAAAP,QAAA,CAC1C3C,SAAS,CAACkE,MAAM,CAAC,CAACC,GAAG,CAAEC,CAAC,GAAKD,GAAG,CAAGC,CAAC,CAACC,iBAAiB,CAAE,CAAC,CAAC,CAACpC,cAAc,CAAC,CAAC,CAClE,CAAC,cACbrC,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAAC,oBAEnD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,CACK,CAAC,CACV,CAAC,CACH,CAAC,cACP/C,IAAA,CAACd,IAAI,EAAC8D,IAAI,CAAE,CAAEkB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAApB,QAAA,cAC5B/C,IAAA,CAAChB,IAAI,EAAA+D,QAAA,cACH/C,IAAA,CAACf,WAAW,EAAA8D,QAAA,cACV7C,KAAA,CAACpB,GAAG,EAAC6D,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACuB,GAAG,CAAE,CAAE,CAAArB,QAAA,eAC7C/C,IAAA,CAACH,QAAQ,EAACyD,KAAK,CAAC,MAAM,CAAE,CAAC,cACzBpD,KAAA,CAACpB,GAAG,EAAAiE,QAAA,eACF/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACE,KAAK,CAAC,WAAW,CAAAP,QAAA,CACvCnB,cAAc,CAACxB,SAAS,CAACkE,MAAM,CAAC,CAACC,GAAG,CAAEC,CAAC,GAAKD,GAAG,CAAGC,CAAC,CAACE,WAAW,CAAE,CAAC,CAAC,CAAC,CAC3D,CAAC,cACb1E,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAAC,aAEnD,CAAY,CAAC,EACV,CAAC,EACH,CAAC,CACK,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,cAGP/C,IAAA,CAAChB,IAAI,EAAA+D,QAAA,cACH7C,KAAA,CAACjB,WAAW,EAAA8D,QAAA,eACV7C,KAAA,CAACnB,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAN,QAAA,EAAC,aACzB,CAACzC,iBAAiB,CAAC+D,MAAM,CAAC,GACvC,EAAY,CAAC,CAEZ/D,iBAAiB,CAAC+D,MAAM,GAAK,CAAC,cAC7BrE,IAAA,CAAClB,GAAG,EAAC6D,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/E/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAP,QAAA,CAC/CnC,WAAW,CAAG,yCAAyC,CAAG,6BAA6B,CAC9E,CAAC,CACV,CAAC,cAENZ,IAAA,CAACZ,IAAI,EAAA2D,QAAA,CACFzC,iBAAiB,CAACqE,GAAG,CAAC,CAACpD,OAAO,CAAEqD,KAAK,gBACpC1E,KAAA,CAACb,QAAQ,EAEPwF,OAAO,CAAED,KAAK,CAAGtE,iBAAiB,CAAC+D,MAAM,CAAG,CAAE,CAC9Cd,EAAE,CAAE,CACF,SAAS,CAAE,CACTuB,eAAe,CAAE,cACnB,CACF,CAAE,CAAA/B,QAAA,eAEF/C,IAAA,CAACV,YAAY,EACXyF,OAAO,cACL7E,KAAA,CAACpB,GAAG,EAAC6D,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACuB,GAAG,CAAE,CAAE,CAAArB,QAAA,eAC7C/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,WAAW,CAAC4B,UAAU,CAAE,GAAI,CAAAjC,QAAA,CAC7CxB,OAAO,CAACC,MAAM,CACL,CAAC,cACbxB,IAAA,CAACR,IAAI,EACHyF,KAAK,IAAA9C,MAAA,CAAKZ,OAAO,CAACkD,iBAAiB,iBAAgB,CACnDzB,IAAI,CAAC,OAAO,CACZI,OAAO,CAAC,UAAU,CAClBE,KAAK,CAAC,SAAS,CAChB,CAAC,EACC,CACN,CACD4B,SAAS,cACPhF,KAAA,CAACpB,GAAG,EAAAiE,QAAA,eACF/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,cAAc,CAAAP,QAAA,CAC7CxB,OAAO,CAACI,YAAY,CACX,CAAC,cACbzB,KAAA,CAACnB,UAAU,EAACqE,OAAO,CAAC,SAAS,CAACE,KAAK,CAAC,gBAAgB,CAAAP,QAAA,EACjDxB,OAAO,CAAC4D,eAAe,CAAC,yCACV,CAAC7C,UAAU,CAACf,OAAO,CAAC6D,uBAAuB,CAAC,EACjD,CAAC,EACV,CACN,CACF,CAAC,cACFpF,IAAA,CAACT,uBAAuB,EAAAwD,QAAA,cACtB7C,KAAA,CAACpB,GAAG,EAACuG,SAAS,CAAC,OAAO,CAAAtC,QAAA,eACpB/C,IAAA,CAACjB,UAAU,EAACqE,OAAO,CAAC,WAAW,CAAC4B,UAAU,CAAE,GAAI,CAAC1B,KAAK,CAAC,cAAc,CAAAP,QAAA,CAClEnB,cAAc,CAACL,OAAO,CAACmD,WAAW,CAAC,CAC1B,CAAC,cACbxE,KAAA,CAACnB,UAAU,EAACqE,OAAO,CAAC,SAAS,CAACE,KAAK,CAAC,cAAc,CAAAP,QAAA,EAAC,OAC5C,CAACnB,cAAc,CAACL,OAAO,CAAC+D,eAAe,CAAC,EACnC,CAAC,cACbtF,IAAA,QAAK,CAAC,cACNE,KAAA,CAACnB,UAAU,EAACqE,OAAO,CAAC,SAAS,CAACE,KAAK,CAAC,YAAY,CAAAP,QAAA,EAAC,QACzC,CAACnB,cAAc,CAACL,OAAO,CAACgE,gBAAgB,CAAC,EACrC,CAAC,EACV,CAAC,CACiB,CAAC,GA/CrBhE,OAAO,CAACC,MAgDL,CACX,CAAC,CACE,CACP,EACU,CAAC,CACV,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAArB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}