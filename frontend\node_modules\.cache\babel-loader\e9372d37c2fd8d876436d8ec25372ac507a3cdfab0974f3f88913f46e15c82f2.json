{"ast": null, "code": "import React,{useState}from'react';import{AppBar,Box,CssBaseline,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Toolbar,Typography,useTheme,useMediaQuery}from'@mui/material';import{Menu as MenuIcon,Dashboard as DashboardIcon,TableChart as TableChartIcon,Analytics as AnalyticsIcon,Business as BusinessIcon,TrendingUp as TrendingUpIcon}from'@mui/icons-material';import{useNavigate,useLocation}from'react-router-dom';import LastFetchedStatus from'./LastFetchedStatus';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const drawerWidth=240;const Layout=_ref=>{let{children}=_ref;const[mobileOpen,setMobileOpen]=useState(false);const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('md'));const navigate=useNavigate();const location=useLocation();const handleDrawerToggle=()=>{setMobileOpen(!mobileOpen);};const menuItems=[{text:'Dashboard',icon:/*#__PURE__*/_jsx(DashboardIcon,{}),path:'/'},{text:'Transactions',icon:/*#__PURE__*/_jsx(TableChartIcon,{}),path:'/transactions'},{text:'Analytics',icon:/*#__PURE__*/_jsx(AnalyticsIcon,{}),path:'/analytics'},{text:'Companies',icon:/*#__PURE__*/_jsx(BusinessIcon,{}),path:'/companies'}];const drawer=/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Toolbar,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(TrendingUpIcon,{color:\"primary\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",color:\"primary\",children:\"NSE Insider\"})]})}),/*#__PURE__*/_jsx(List,{children:menuItems.map(item=>/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsxs(ListItemButton,{selected:location.pathname===item.path,onClick:()=>{navigate(item.path);if(isMobile){setMobileOpen(false);}},sx:{'&.Mui-selected':{backgroundColor:theme.palette.primary.main+'20','&:hover':{backgroundColor:theme.palette.primary.main+'30'}}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{color:location.pathname===item.path?theme.palette.primary.main:'inherit'},children:item.icon}),/*#__PURE__*/_jsx(ListItemText,{primary:item.text,sx:{'& .MuiListItemText-primary':{color:location.pathname===item.path?theme.palette.primary.main:'inherit',fontWeight:location.pathname===item.path?600:400}}})]})},item.text))})]});return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex'},children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(AppBar,{position:\"fixed\",sx:{width:{md:\"calc(100% - \".concat(drawerWidth,\"px)\")},ml:{md:\"\".concat(drawerWidth,\"px\")},backgroundColor:'white',color:'text.primary',boxShadow:'0 1px 3px rgba(0,0,0,0.12)'},children:/*#__PURE__*/_jsxs(Toolbar,{children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",\"aria-label\":\"open drawer\",edge:\"start\",onClick:handleDrawerToggle,sx:{mr:2,display:{md:'none'}},children:/*#__PURE__*/_jsx(MenuIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",sx:{flexGrow:1},children:\"NSE Insider Trading Dashboard\"}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:/*#__PURE__*/_jsx(LastFetchedStatus,{compact:true})})]})}),/*#__PURE__*/_jsxs(Box,{component:\"nav\",sx:{width:{md:drawerWidth},flexShrink:{md:0}},\"aria-label\":\"navigation menu\",children:[/*#__PURE__*/_jsx(Drawer,{variant:\"temporary\",open:mobileOpen,onClose:handleDrawerToggle,ModalProps:{keepMounted:true// Better open performance on mobile.\n},sx:{display:{xs:'block',md:'none'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:drawerWidth}},children:drawer}),/*#__PURE__*/_jsx(Drawer,{variant:\"permanent\",sx:{display:{xs:'none',md:'block'},'& .MuiDrawer-paper':{boxSizing:'border-box',width:drawerWidth}},open:true,children:drawer})]}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,p:3,width:{md:\"calc(100% - \".concat(drawerWidth,\"px)\")},mt:8,// Account for AppBar height\nbackgroundColor:theme.palette.background.default,minHeight:'100vh'},children:children})]});};export default Layout;", "map": {"version": 3, "names": ["React", "useState", "AppBar", "Box", "CssBaseline", "Drawer", "IconButton", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON><PERSON>", "Typography", "useTheme", "useMediaQuery", "<PERSON><PERSON>", "MenuIcon", "Dashboard", "DashboardIcon", "Table<PERSON>hart", "TableChartIcon", "Analytics", "AnalyticsIcon", "Business", "BusinessIcon", "TrendingUp", "TrendingUpIcon", "useNavigate", "useLocation", "LastFetchedStatus", "jsx", "_jsx", "jsxs", "_jsxs", "drawerWidth", "Layout", "_ref", "children", "mobileOpen", "setMobileOpen", "theme", "isMobile", "breakpoints", "down", "navigate", "location", "handleDrawerToggle", "menuItems", "text", "icon", "path", "drawer", "sx", "display", "alignItems", "gap", "color", "variant", "noWrap", "component", "map", "item", "disablePadding", "selected", "pathname", "onClick", "backgroundColor", "palette", "primary", "main", "fontWeight", "position", "width", "md", "concat", "ml", "boxShadow", "edge", "mr", "flexGrow", "compact", "flexShrink", "open", "onClose", "ModalProps", "keepMounted", "xs", "boxSizing", "p", "mt", "background", "default", "minHeight"], "sources": ["D:/chirag/nsescrapper/frontend/src/components/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  AppBar,\n  Box,\n  CssBaseline,\n  Drawer,\n  IconButton,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Toolbar,\n  Typography,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  Dashboard as DashboardIcon,\n  TableChart as TableChartIcon,\n  Analytics as AnalyticsIcon,\n  Business as BusinessIcon,\n  TrendingUp as TrendingUpIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport LastFetchedStatus from './LastFetchedStatus';\n\nconst drawerWidth = 240;\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const menuItems = [\n    {\n      text: 'Dashboard',\n      icon: <DashboardIcon />,\n      path: '/',\n    },\n    {\n      text: 'Transactions',\n      icon: <TableChartIcon />,\n      path: '/transactions',\n    },\n    {\n      text: 'Analytics',\n      icon: <AnalyticsIcon />,\n      path: '/analytics',\n    },\n    {\n      text: 'Companies',\n      icon: <BusinessIcon />,\n      path: '/companies',\n    },\n  ];\n\n  const drawer = (\n    <div>\n      <Toolbar>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <TrendingUpIcon color=\"primary\" />\n          <Typography variant=\"h6\" noWrap component=\"div\" color=\"primary\">\n            NSE Insider\n          </Typography>\n        </Box>\n      </Toolbar>\n      <List>\n        {menuItems.map((item) => (\n          <ListItem key={item.text} disablePadding>\n            <ListItemButton\n              selected={location.pathname === item.path}\n              onClick={() => {\n                navigate(item.path);\n                if (isMobile) {\n                  setMobileOpen(false);\n                }\n              }}\n              sx={{\n                '&.Mui-selected': {\n                  backgroundColor: theme.palette.primary.main + '20',\n                  '&:hover': {\n                    backgroundColor: theme.palette.primary.main + '30',\n                  },\n                },\n              }}\n            >\n              <ListItemIcon\n                sx={{\n                  color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',\n                }}\n              >\n                {item.icon}\n              </ListItemIcon>\n              <ListItemText\n                primary={item.text}\n                sx={{\n                  '& .MuiListItemText-primary': {\n                    color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',\n                    fontWeight: location.pathname === item.path ? 600 : 400,\n                  },\n                }}\n              />\n            </ListItemButton>\n          </ListItem>\n        ))}\n      </List>\n    </div>\n  );\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <CssBaseline />\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: { md: `calc(100% - ${drawerWidth}px)` },\n          ml: { md: `${drawerWidth}px` },\n          backgroundColor: 'white',\n          color: 'text.primary',\n          boxShadow: '0 1px 3px rgba(0,0,0,0.12)',\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2, display: { md: 'none' } }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            NSE Insider Trading Dashboard\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <LastFetchedStatus compact />\n          </Box>\n        </Toolbar>\n      </AppBar>\n      <Box\n        component=\"nav\"\n        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}\n        aria-label=\"navigation menu\"\n      >\n        {/* Mobile drawer */}\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{\n            keepMounted: true, // Better open performance on mobile.\n          }}\n          sx={{\n            display: { xs: 'block', md: 'none' },\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        {/* Desktop drawer */}\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            display: { xs: 'none', md: 'block' },\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n            },\n          }}\n          open\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { md: `calc(100% - ${drawerWidth}px)` },\n          mt: 8, // Account for AppBar height\n          backgroundColor: theme.palette.background.default,\n          minHeight: '100vh',\n        }}\n      >\n        {children}\n      </Box>\n    </Box>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,MAAM,CACNC,GAAG,CACHC,WAAW,CACXC,MAAM,CACNC,UAAU,CACVC,IAAI,CACJC,QAAQ,CACRC,cAAc,CACdC,YAAY,CACZC,YAAY,CACZC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,aAAa,KACR,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,KACvB,qBAAqB,CAC5B,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,WAAW,CAAG,GAAG,CAMvB,KAAM,CAAAC,MAA6B,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjD,KAAM,CAACE,UAAU,CAAEC,aAAa,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAAwC,KAAK,CAAG3B,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA4B,QAAQ,CAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAkB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BP,aAAa,CAAC,CAACD,UAAU,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAS,SAAS,CAAG,CAChB,CACEC,IAAI,CAAE,WAAW,CACjBC,IAAI,cAAElB,IAAA,CAACb,aAAa,GAAE,CAAC,CACvBgC,IAAI,CAAE,GACR,CAAC,CACD,CACEF,IAAI,CAAE,cAAc,CACpBC,IAAI,cAAElB,IAAA,CAACX,cAAc,GAAE,CAAC,CACxB8B,IAAI,CAAE,eACR,CAAC,CACD,CACEF,IAAI,CAAE,WAAW,CACjBC,IAAI,cAAElB,IAAA,CAACT,aAAa,GAAE,CAAC,CACvB4B,IAAI,CAAE,YACR,CAAC,CACD,CACEF,IAAI,CAAE,WAAW,CACjBC,IAAI,cAAElB,IAAA,CAACP,YAAY,GAAE,CAAC,CACtB0B,IAAI,CAAE,YACR,CAAC,CACF,CAED,KAAM,CAAAC,MAAM,cACVlB,KAAA,QAAAI,QAAA,eACEN,IAAA,CAACpB,OAAO,EAAA0B,QAAA,cACNJ,KAAA,CAAC/B,GAAG,EAACkD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAlB,QAAA,eACzDN,IAAA,CAACL,cAAc,EAAC8B,KAAK,CAAC,SAAS,CAAE,CAAC,cAClCzB,IAAA,CAACnB,UAAU,EAAC6C,OAAO,CAAC,IAAI,CAACC,MAAM,MAACC,SAAS,CAAC,KAAK,CAACH,KAAK,CAAC,SAAS,CAAAnB,QAAA,CAAC,aAEhE,CAAY,CAAC,EACV,CAAC,CACC,CAAC,cACVN,IAAA,CAACzB,IAAI,EAAA+B,QAAA,CACFU,SAAS,CAACa,GAAG,CAAEC,IAAI,eAClB9B,IAAA,CAACxB,QAAQ,EAAiBuD,cAAc,MAAAzB,QAAA,cACtCJ,KAAA,CAACzB,cAAc,EACbuD,QAAQ,CAAElB,QAAQ,CAACmB,QAAQ,GAAKH,IAAI,CAACX,IAAK,CAC1Ce,OAAO,CAAEA,CAAA,GAAM,CACbrB,QAAQ,CAACiB,IAAI,CAACX,IAAI,CAAC,CACnB,GAAIT,QAAQ,CAAE,CACZF,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAE,CACFa,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBc,eAAe,CAAE1B,KAAK,CAAC2B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAG,IAAI,CAClD,SAAS,CAAE,CACTH,eAAe,CAAE1B,KAAK,CAAC2B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAG,IAChD,CACF,CACF,CAAE,CAAAhC,QAAA,eAEFN,IAAA,CAACtB,YAAY,EACX2C,EAAE,CAAE,CACFI,KAAK,CAAEX,QAAQ,CAACmB,QAAQ,GAAKH,IAAI,CAACX,IAAI,CAAGV,KAAK,CAAC2B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAG,SACxE,CAAE,CAAAhC,QAAA,CAEDwB,IAAI,CAACZ,IAAI,CACE,CAAC,cACflB,IAAA,CAACrB,YAAY,EACX0D,OAAO,CAAEP,IAAI,CAACb,IAAK,CACnBI,EAAE,CAAE,CACF,4BAA4B,CAAE,CAC5BI,KAAK,CAAEX,QAAQ,CAACmB,QAAQ,GAAKH,IAAI,CAACX,IAAI,CAAGV,KAAK,CAAC2B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAG,SAAS,CAC/EC,UAAU,CAAEzB,QAAQ,CAACmB,QAAQ,GAAKH,IAAI,CAACX,IAAI,CAAG,GAAG,CAAG,GACtD,CACF,CAAE,CACH,CAAC,EACY,CAAC,EAlCJW,IAAI,CAACb,IAmCV,CACX,CAAC,CACE,CAAC,EACJ,CACN,CAED,mBACEf,KAAA,CAAC/B,GAAG,EAACkD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAhB,QAAA,eAC3BN,IAAA,CAAC5B,WAAW,GAAE,CAAC,cACf4B,IAAA,CAAC9B,MAAM,EACLsE,QAAQ,CAAC,OAAO,CAChBnB,EAAE,CAAE,CACFoB,KAAK,CAAE,CAAEC,EAAE,gBAAAC,MAAA,CAAiBxC,WAAW,OAAM,CAAC,CAC9CyC,EAAE,CAAE,CAAEF,EAAE,IAAAC,MAAA,CAAKxC,WAAW,MAAK,CAAC,CAC9BgC,eAAe,CAAE,OAAO,CACxBV,KAAK,CAAE,cAAc,CACrBoB,SAAS,CAAE,4BACb,CAAE,CAAAvC,QAAA,cAEFJ,KAAA,CAACtB,OAAO,EAAA0B,QAAA,eACNN,IAAA,CAAC1B,UAAU,EACTmD,KAAK,CAAC,SAAS,CACf,aAAW,aAAa,CACxBqB,IAAI,CAAC,OAAO,CACZZ,OAAO,CAAEnB,kBAAmB,CAC5BM,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAC,CAAEzB,OAAO,CAAE,CAAEoB,EAAE,CAAE,MAAO,CAAE,CAAE,CAAApC,QAAA,cAEvCN,IAAA,CAACf,QAAQ,GAAE,CAAC,CACF,CAAC,cACbe,IAAA,CAACnB,UAAU,EAAC6C,OAAO,CAAC,IAAI,CAACC,MAAM,MAACC,SAAS,CAAC,KAAK,CAACP,EAAE,CAAE,CAAE2B,QAAQ,CAAE,CAAE,CAAE,CAAA1C,QAAA,CAAC,+BAErE,CAAY,CAAC,cACbN,IAAA,CAAC7B,GAAG,EAACkD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACzDN,IAAA,CAACF,iBAAiB,EAACmD,OAAO,MAAE,CAAC,CAC1B,CAAC,EACC,CAAC,CACJ,CAAC,cACT/C,KAAA,CAAC/B,GAAG,EACFyD,SAAS,CAAC,KAAK,CACfP,EAAE,CAAE,CAAEoB,KAAK,CAAE,CAAEC,EAAE,CAAEvC,WAAY,CAAC,CAAE+C,UAAU,CAAE,CAAER,EAAE,CAAE,CAAE,CAAE,CAAE,CAC1D,aAAW,iBAAiB,CAAApC,QAAA,eAG5BN,IAAA,CAAC3B,MAAM,EACLqD,OAAO,CAAC,WAAW,CACnByB,IAAI,CAAE5C,UAAW,CACjB6C,OAAO,CAAErC,kBAAmB,CAC5BsC,UAAU,CAAE,CACVC,WAAW,CAAE,IAAM;AACrB,CAAE,CACFjC,EAAE,CAAE,CACFC,OAAO,CAAE,CAAEiC,EAAE,CAAE,OAAO,CAAEb,EAAE,CAAE,MAAO,CAAC,CACpC,oBAAoB,CAAE,CACpBc,SAAS,CAAE,YAAY,CACvBf,KAAK,CAAEtC,WACT,CACF,CAAE,CAAAG,QAAA,CAEDc,MAAM,CACD,CAAC,cAETpB,IAAA,CAAC3B,MAAM,EACLqD,OAAO,CAAC,WAAW,CACnBL,EAAE,CAAE,CACFC,OAAO,CAAE,CAAEiC,EAAE,CAAE,MAAM,CAAEb,EAAE,CAAE,OAAQ,CAAC,CACpC,oBAAoB,CAAE,CACpBc,SAAS,CAAE,YAAY,CACvBf,KAAK,CAAEtC,WACT,CACF,CAAE,CACFgD,IAAI,MAAA7C,QAAA,CAEHc,MAAM,CACD,CAAC,EACN,CAAC,cACNpB,IAAA,CAAC7B,GAAG,EACFyD,SAAS,CAAC,MAAM,CAChBP,EAAE,CAAE,CACF2B,QAAQ,CAAE,CAAC,CACXS,CAAC,CAAE,CAAC,CACJhB,KAAK,CAAE,CAAEC,EAAE,gBAAAC,MAAA,CAAiBxC,WAAW,OAAM,CAAC,CAC9CuD,EAAE,CAAE,CAAC,CAAE;AACPvB,eAAe,CAAE1B,KAAK,CAAC2B,OAAO,CAACuB,UAAU,CAACC,OAAO,CACjDC,SAAS,CAAE,OACb,CAAE,CAAAvD,QAAA,CAEDA,QAAQ,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}