export GCLOUD_PROJECT="virtual-indexer-454511-p6" 
# from Step 2.2 above:
export REPO="insidetradingscrape"
# the region you chose in Step 2.4:
export REGION="asia-south1"
# whatever you want to call this image:
export IMAGE="insidetradingscrape-project-image"

# use the region you chose above here in the URL:
export IMAGE_TAG=${REGION}-docker.pkg.dev/$GCLOUD_PROJECT/$REPO/$IMAGE

# Build the image:
docker build -t $IMAGE_TAG -f path/to/Dockerfile --platform linux/x86_64 .
# Push it to Artifact Registry:
docker push $IMAGE_TAG
