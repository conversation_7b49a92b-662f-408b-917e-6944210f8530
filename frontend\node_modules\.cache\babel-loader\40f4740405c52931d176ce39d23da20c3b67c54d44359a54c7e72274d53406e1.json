{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Grid,Card,CardContent,CircularProgress,Alert}from'@mui/material';import{apiService}from'../services/apiService';import TopCompaniesChart from'../components/TopCompaniesChart';import TransactionTrendsChart from'../components/TransactionTrendsChart';import StatCard from'../components/StatCard';import{TrendingUp,TrendingDown,Business}from'@mui/icons-material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Analytics=()=>{const[summary,setSummary]=useState(null);const[topCompanies,setTopCompanies]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);useEffect(()=>{const fetchAnalyticsData=async()=>{try{setLoading(true);setError(null);const[summaryResponse,companiesResponse]=await Promise.all([apiService.getAnalyticsSummary(),apiService.getTopCompanies({limit:20})]);setSummary(summaryResponse.data);setTopCompanies(companiesResponse.data);}catch(err){console.error('Error fetching analytics data:',err);setError('Failed to load analytics data');}finally{setLoading(false);}};fetchAnalyticsData();},[]);const formatCurrency=value=>{if(value===null||value===undefined||isNaN(value)){return'₹0';}const absValue=Math.abs(value);if(absValue>=10000000){return\"\\u20B9\".concat((value/10000000).toFixed(1),\"Cr\");}else if(absValue>=100000){return\"\\u20B9\".concat((value/100000).toFixed(1),\"L\");}else{return\"\\u20B9\".concat(value.toLocaleString());}};if(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{size:60})});}if(error){return/*#__PURE__*/_jsx(Box,{p:3,children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error})});}const netValue=summary?summary.net_value:0;return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{mb:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Analytics\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"Detailed analysis of insider trading patterns and trends\"})]}),summary&&/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Total Buy Value\",value:formatCurrency(summary.total_buy_value),icon:/*#__PURE__*/_jsx(TrendingUp,{}),color:\"success\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Total Sell Value\",value:formatCurrency(summary.total_sell_value),icon:/*#__PURE__*/_jsx(TrendingDown,{}),color:\"error\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Net Value\",value:formatCurrency(Math.abs(netValue)),subtitle:netValue>=0?'Net Buying':'Net Selling',icon:netValue>=0?/*#__PURE__*/_jsx(TrendingUp,{}):/*#__PURE__*/_jsx(TrendingDown,{}),color:netValue>=0?'success':'error'})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Active Companies\",value:summary.unique_companies.toLocaleString(),icon:/*#__PURE__*/_jsx(Business,{}),color:\"info\"})})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,lg:8},children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Transaction Trends (Last 30 Days)\"}),/*#__PURE__*/_jsx(TransactionTrendsChart,{})]})})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,lg:4},children:/*#__PURE__*/_jsx(Card,{sx:{height:'100%'},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Top Companies by Value\"}),/*#__PURE__*/_jsx(Box,{sx:{height:400,overflow:'auto'},children:topCompanies.slice(0,10).map((company,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',p:1,borderBottom:index<9?'1px solid':'none',borderBottomColor:'divider'},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:600,children:company.symbol}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",children:[company.transaction_count,\" transactions\"]})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:600,color:\"primary.main\",children:formatCurrency(company.total_value)})]},company.symbol))})]})})}),/*#__PURE__*/_jsx(Grid,{size:12,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Top 10 Companies by Transaction Value\"}),/*#__PURE__*/_jsx(TopCompaniesChart,{data:topCompanies})]})})})]})]});};export default Analytics;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "apiService", "TopCompaniesChart", "TransactionTrendsChart", "StatCard", "TrendingUp", "TrendingDown", "Business", "jsx", "_jsx", "jsxs", "_jsxs", "Analytics", "summary", "set<PERSON>ummary", "topCompanies", "setTopCompanies", "loading", "setLoading", "error", "setError", "fetchAnalyticsData", "summaryResponse", "companiesResponse", "Promise", "all", "getAnalyticsSummary", "getTopCompanies", "limit", "data", "err", "console", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "concat", "toFixed", "toLocaleString", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "p", "severity", "netValue", "net_value", "mb", "variant", "gutterBottom", "color", "container", "spacing", "sx", "xs", "sm", "md", "title", "total_buy_value", "icon", "total_sell_value", "subtitle", "unique_companies", "lg", "height", "overflow", "slice", "map", "company", "index", "borderBottom", "borderBottomColor", "fontWeight", "symbol", "transaction_count", "total_value"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Analytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport { apiService } from '../services/apiService';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport StatCard from '../components/StatCard';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Business,\n  Person,\n} from '@mui/icons-material';\n\nconst Analytics: React.FC = () => {\n  const [summary, setSummary] = useState<any>(null);\n  const [topCompanies, setTopCompanies] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchAnalyticsData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const [summaryResponse, companiesResponse] = await Promise.all([\n          apiService.getAnalyticsSummary(),\n          apiService.getTopCompanies({ limit: 20 }),\n        ]);\n\n        setSummary(summaryResponse.data);\n        setTopCompanies(companiesResponse.data);\n      } catch (err) {\n        console.error('Error fetching analytics data:', err);\n        setError('Failed to load analytics data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchAnalyticsData();\n  }, []);\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  const netValue = summary ? summary.net_value : 0;\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Analytics\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Detailed analysis of insider trading patterns and trends\n        </Typography>\n      </Box>\n\n      {/* Summary Stats */}\n      {summary && (\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Total Buy Value\"\n              value={formatCurrency(summary.total_buy_value)}\n              icon={<TrendingUp />}\n              color=\"success\"\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Total Sell Value\"\n              value={formatCurrency(summary.total_sell_value)}\n              icon={<TrendingDown />}\n              color=\"error\"\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Net Value\"\n              value={formatCurrency(Math.abs(netValue))}\n              subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}\n              icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}\n              color={netValue >= 0 ? 'success' : 'error'}\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Active Companies\"\n              value={summary.unique_companies.toLocaleString()}\n              icon={<Business />}\n              color=\"info\"\n            />\n          </Grid>\n        </Grid>\n      )}\n\n      {/* Charts */}\n      <Grid container spacing={3}>\n        {/* Transaction Trends */}\n        <Grid size={{ xs: 12, lg: 8 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Transaction Trends (Last 30 Days)\n              </Typography>\n              <TransactionTrendsChart />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Top Companies */}\n        <Grid size={{ xs: 12, lg: 4 }}>\n          <Card sx={{ height: '100%' }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top Companies by Value\n              </Typography>\n              <Box sx={{ height: 400, overflow: 'auto' }}>\n                {topCompanies.slice(0, 10).map((company, index) => (\n                  <Box\n                    key={company.symbol}\n                    sx={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      p: 1,\n                      borderBottom: index < 9 ? '1px solid' : 'none',\n                      borderBottomColor: 'divider',\n                    }}\n                  >\n                    <Box>\n                      <Typography variant=\"body2\" fontWeight={600}>\n                        {company.symbol}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {company.transaction_count} transactions\n                      </Typography>\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight={600} color=\"primary.main\">\n                      {formatCurrency(company.total_value)}\n                    </Typography>\n                  </Box>\n                ))}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Top Companies Chart */}\n        <Grid size={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top 10 Companies by Transaction Value\n              </Typography>\n              <TopCompaniesChart data={topCompanies} />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Analytics;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,gBAAgB,CAChBC,KAAK,KACA,eAAe,CACtB,OAASC,UAAU,KAAQ,wBAAwB,CACnD,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAC/D,MAAO,CAAAC,sBAAsB,KAAM,sCAAsC,CACzE,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,OACEC,UAAU,CACVC,YAAY,CACZC,QAAQ,KAEH,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7B,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAM,IAAI,CAAC,CACjD,KAAM,CAACuB,YAAY,CAAEC,eAAe,CAAC,CAAGxB,QAAQ,CAAQ,EAAE,CAAC,CAC3D,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CAEvDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4B,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAACE,eAAe,CAAEC,iBAAiB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC7DxB,UAAU,CAACyB,mBAAmB,CAAC,CAAC,CAChCzB,UAAU,CAAC0B,eAAe,CAAC,CAAEC,KAAK,CAAE,EAAG,CAAC,CAAC,CAC1C,CAAC,CAEFd,UAAU,CAACQ,eAAe,CAACO,IAAI,CAAC,CAChCb,eAAe,CAACO,iBAAiB,CAACM,IAAI,CAAC,CACzC,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACZ,KAAK,CAAC,gCAAgC,CAAEW,GAAG,CAAC,CACpDV,QAAQ,CAAC,+BAA+B,CAAC,CAC3C,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,cAAc,CAAIC,KAAgC,EAAK,CAC3D,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAIC,KAAK,CAACF,KAAK,CAAC,CAAE,CACzD,MAAO,IAAI,CACb,CAEA,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,CAChC,GAAIG,QAAQ,EAAI,QAAQ,CAAE,CACxB,eAAAG,MAAA,CAAW,CAACN,KAAK,CAAG,QAAQ,EAAEO,OAAO,CAAC,CAAC,CAAC,OAC1C,CAAC,IAAM,IAAIJ,QAAQ,EAAI,MAAM,CAAE,CAC7B,eAAAG,MAAA,CAAW,CAACN,KAAK,CAAG,MAAM,EAAEO,OAAO,CAAC,CAAC,CAAC,MACxC,CAAC,IAAM,CACL,eAAAD,MAAA,CAAWN,KAAK,CAACQ,cAAc,CAAC,CAAC,EACnC,CACF,CAAC,CAED,GAAIxB,OAAO,CAAE,CACX,mBACER,IAAA,CAACf,GAAG,EAACgD,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/ErC,IAAA,CAACV,gBAAgB,EAACgD,IAAI,CAAE,EAAG,CAAE,CAAC,CAC3B,CAAC,CAEV,CAEA,GAAI5B,KAAK,CAAE,CACT,mBACEV,IAAA,CAACf,GAAG,EAACsD,CAAC,CAAE,CAAE,CAAAF,QAAA,cACRrC,IAAA,CAACT,KAAK,EAACiD,QAAQ,CAAC,OAAO,CAAAH,QAAA,CAAE3B,KAAK,CAAQ,CAAC,CACpC,CAAC,CAEV,CAEA,KAAM,CAAA+B,QAAQ,CAAGrC,OAAO,CAAGA,OAAO,CAACsC,SAAS,CAAG,CAAC,CAEhD,mBACExC,KAAA,CAACjB,GAAG,EAAAoD,QAAA,eAEFnC,KAAA,CAACjB,GAAG,EAAC0D,EAAE,CAAE,CAAE,CAAAN,QAAA,eACTrC,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CAAC,WAEtC,CAAY,CAAC,cACbrC,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CAAC,0DAEnD,CAAY,CAAC,EACV,CAAC,CAGLjC,OAAO,eACNF,KAAA,CAACf,IAAI,EAAC4D,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEN,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eACxCrC,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,CAAEY,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAf,QAAA,cACnCrC,IAAA,CAACL,QAAQ,EACP0D,KAAK,CAAC,iBAAiB,CACvB7B,KAAK,CAAED,cAAc,CAACnB,OAAO,CAACkD,eAAe,CAAE,CAC/CC,IAAI,cAAEvD,IAAA,CAACJ,UAAU,GAAE,CAAE,CACrBkD,KAAK,CAAC,SAAS,CAChB,CAAC,CACE,CAAC,cACP9C,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,CAAEY,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAf,QAAA,cACnCrC,IAAA,CAACL,QAAQ,EACP0D,KAAK,CAAC,kBAAkB,CACxB7B,KAAK,CAAED,cAAc,CAACnB,OAAO,CAACoD,gBAAgB,CAAE,CAChDD,IAAI,cAAEvD,IAAA,CAACH,YAAY,GAAE,CAAE,CACvBiD,KAAK,CAAC,OAAO,CACd,CAAC,CACE,CAAC,cACP9C,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,CAAEY,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAf,QAAA,cACnCrC,IAAA,CAACL,QAAQ,EACP0D,KAAK,CAAC,WAAW,CACjB7B,KAAK,CAAED,cAAc,CAACK,IAAI,CAACC,GAAG,CAACY,QAAQ,CAAC,CAAE,CAC1CgB,QAAQ,CAAEhB,QAAQ,EAAI,CAAC,CAAG,YAAY,CAAG,aAAc,CACvDc,IAAI,CAAEd,QAAQ,EAAI,CAAC,cAAGzC,IAAA,CAACJ,UAAU,GAAE,CAAC,cAAGI,IAAA,CAACH,YAAY,GAAE,CAAE,CACxDiD,KAAK,CAAEL,QAAQ,EAAI,CAAC,CAAG,SAAS,CAAG,OAAQ,CAC5C,CAAC,CACE,CAAC,cACPzC,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,CAAEY,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAf,QAAA,cACnCrC,IAAA,CAACL,QAAQ,EACP0D,KAAK,CAAC,kBAAkB,CACxB7B,KAAK,CAAEpB,OAAO,CAACsD,gBAAgB,CAAC1B,cAAc,CAAC,CAAE,CACjDuB,IAAI,cAAEvD,IAAA,CAACF,QAAQ,GAAE,CAAE,CACnBgD,KAAK,CAAC,MAAM,CACb,CAAC,CACE,CAAC,EACH,CACP,cAGD5C,KAAA,CAACf,IAAI,EAAC4D,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAX,QAAA,eAEzBrC,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,CAAEY,EAAE,CAAE,EAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,cAC5BrC,IAAA,CAACZ,IAAI,EAAAiD,QAAA,cACHnC,KAAA,CAACb,WAAW,EAAAgD,QAAA,eACVrC,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CAAC,mCAEtC,CAAY,CAAC,cACbrC,IAAA,CAACN,sBAAsB,GAAE,CAAC,EACf,CAAC,CACV,CAAC,CACH,CAAC,cAGPM,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,CAAEY,EAAE,CAAE,EAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,cAC5BrC,IAAA,CAACZ,IAAI,EAAC6D,EAAE,CAAE,CAAEW,MAAM,CAAE,MAAO,CAAE,CAAAvB,QAAA,cAC3BnC,KAAA,CAACb,WAAW,EAAAgD,QAAA,eACVrC,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CAAC,wBAEtC,CAAY,CAAC,cACbrC,IAAA,CAACf,GAAG,EAACgE,EAAE,CAAE,CAAEW,MAAM,CAAE,GAAG,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAxB,QAAA,CACxC/B,YAAY,CAACwD,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC5C/D,KAAA,CAACjB,GAAG,EAEFgE,EAAE,CAAE,CACFhB,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBI,CAAC,CAAE,CAAC,CACJ2B,YAAY,CAAED,KAAK,CAAG,CAAC,CAAG,WAAW,CAAG,MAAM,CAC9CE,iBAAiB,CAAE,SACrB,CAAE,CAAA9B,QAAA,eAEFnC,KAAA,CAACjB,GAAG,EAAAoD,QAAA,eACFrC,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAACwB,UAAU,CAAE,GAAI,CAAA/B,QAAA,CACzC2B,OAAO,CAACK,MAAM,CACL,CAAC,cACbnE,KAAA,CAAChB,UAAU,EAAC0D,OAAO,CAAC,SAAS,CAACE,KAAK,CAAC,gBAAgB,CAAAT,QAAA,EACjD2B,OAAO,CAACM,iBAAiB,CAAC,eAC7B,EAAY,CAAC,EACV,CAAC,cACNtE,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAACwB,UAAU,CAAE,GAAI,CAACtB,KAAK,CAAC,cAAc,CAAAT,QAAA,CAC9Dd,cAAc,CAACyC,OAAO,CAACO,WAAW,CAAC,CAC1B,CAAC,GApBRP,OAAO,CAACK,MAqBV,CACN,CAAC,CACC,CAAC,EACK,CAAC,CACV,CAAC,CACH,CAAC,cAGPrE,IAAA,CAACb,IAAI,EAACmD,IAAI,CAAE,EAAG,CAAAD,QAAA,cACbrC,IAAA,CAACZ,IAAI,EAAAiD,QAAA,cACHnC,KAAA,CAACb,WAAW,EAAAgD,QAAA,eACVrC,IAAA,CAACd,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CAAC,uCAEtC,CAAY,CAAC,cACbrC,IAAA,CAACP,iBAAiB,EAAC2B,IAAI,CAAEd,YAAa,CAAE,CAAC,EAC9B,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}