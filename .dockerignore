# Node modules (will be installed during build)
**/node_modules/
frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Python cache and virtual environments
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
**/.venv/
**/venv/
**/env/
**/ENV/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Deployment files
deploy-gcp.sh
.dockerignore
README.md
DEPLOYMENT.md

# Test files
tests/
test/
*.test.js
*.test.py

# Documentation
docs/
*.md

# CSV data files
*.csv

# Temporary files
tmp/
temp/
