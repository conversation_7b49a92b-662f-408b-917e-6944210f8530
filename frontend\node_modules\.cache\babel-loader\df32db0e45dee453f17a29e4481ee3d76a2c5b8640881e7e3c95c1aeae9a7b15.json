{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Grid,Card,CardContent,Typography,CircularProgress,Alert}from'@mui/material';import{TrendingUp,TrendingDown,Business,AccountBalance}from'@mui/icons-material';import{apiService}from'../services/apiService';import StatCard from'../components/StatCard';import RecentTransactions from'../components/RecentTransactions';import TopCompaniesChart from'../components/TopCompaniesChart';import TransactionTrendsChart from'../components/TransactionTrendsChart';import LastFetchedStatus from'../components/LastFetchedStatus';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Dashboard=()=>{const[data,setData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);useEffect(()=>{const fetchDashboardData=async()=>{try{setLoading(true);setError(null);// Fetch all dashboard data in parallel\nconst[summaryResponse,recentTransactionsResponse,topCompaniesResponse,systemStatusResponse]=await Promise.all([apiService.getAnalyticsSummary(),apiService.getTransactions({limit:10,sort_by:'transaction_date',sort_order:'desc'}),apiService.getTopCompanies({limit:10}),apiService.getSystemStatus()]);setData({summary:summaryResponse.data,recent_transactions:recentTransactionsResponse.data.transactions,top_companies:topCompaniesResponse.data,system_status:systemStatusResponse.data});}catch(err){console.error('Error fetching dashboard data:',err);setError('Failed to load dashboard data. Please try again.');}finally{setLoading(false);}};fetchDashboardData();// Set up auto-refresh every 5 minutes\nconst interval=setInterval(fetchDashboardData,5*60*1000);return()=>clearInterval(interval);},[]);if(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{size:60})});}if(error){return/*#__PURE__*/_jsx(Box,{p:3,children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error})});}if(!data){return/*#__PURE__*/_jsx(Box,{p:3,children:/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"No data available\"})});}const{summary,recent_transactions,top_companies,system_status}=data;const formatCurrency=value=>{if(value===null||value===undefined||isNaN(value)){return'₹0';}const absValue=Math.abs(value);if(absValue>=10000000){return\"\\u20B9\".concat((value/10000000).toFixed(1),\"Cr\");}else if(absValue>=100000){return\"\\u20B9\".concat((value/100000).toFixed(1),\"L\");}else{return\"\\u20B9\".concat(value.toLocaleString());}};const netValue=summary.net_value;return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{mb:3,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Dashboard\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:\"Overview of NSE insider trading activities\"})]}),/*#__PURE__*/_jsx(Box,{mb:3,children:/*#__PURE__*/_jsx(LastFetchedStatus,{})}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mb:3},children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:2.4},children:/*#__PURE__*/_jsx(StatCard,{title:\"Total Transactions\",value:summary.total_transactions.toLocaleString(),icon:/*#__PURE__*/_jsx(AccountBalance,{}),color:\"primary\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:2.4},children:/*#__PURE__*/_jsx(StatCard,{title:\"Buy Value\",value:formatCurrency(summary.total_buy_value),icon:/*#__PURE__*/_jsx(TrendingUp,{}),color:\"success\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:2.4},children:/*#__PURE__*/_jsx(StatCard,{title:\"Sell Value\",value:formatCurrency(summary.total_sell_value),icon:/*#__PURE__*/_jsx(TrendingDown,{}),color:\"error\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:2.4},children:/*#__PURE__*/_jsx(StatCard,{title:\"Net Value\",value:formatCurrency(Math.abs(netValue)),subtitle:netValue>=0?'Net Buying':'Net Selling',icon:netValue>=0?/*#__PURE__*/_jsx(TrendingUp,{}):/*#__PURE__*/_jsx(TrendingDown,{}),color:netValue>=0?'success':'error'})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:2.4},children:/*#__PURE__*/_jsx(StatCard,{title:\"Companies\",value:summary.unique_companies.toLocaleString(),icon:/*#__PURE__*/_jsx(Business,{}),color:\"info\"})})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,lg:6},children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Top Companies by Transaction Value\"}),/*#__PURE__*/_jsx(TopCompaniesChart,{data:top_companies})]})})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,lg:6},children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Transaction Trends (Last 30 Days)\"}),/*#__PURE__*/_jsx(TransactionTrendsChart,{})]})})}),/*#__PURE__*/_jsx(Grid,{size:12,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Recent Transactions\"}),/*#__PURE__*/_jsx(RecentTransactions,{transactions:recent_transactions})]})})})]})]});};export default Dashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "TrendingUp", "TrendingDown", "Business", "AccountBalance", "apiService", "StatCard", "RecentTransactions", "TopCompaniesChart", "TransactionTrendsChart", "LastFetchedStatus", "jsx", "_jsx", "jsxs", "_jsxs", "Dashboard", "data", "setData", "loading", "setLoading", "error", "setError", "fetchDashboardData", "summaryResponse", "recentTransactionsResponse", "topCompaniesResponse", "systemStatusResponse", "Promise", "all", "getAnalyticsSummary", "getTransactions", "limit", "sort_by", "sort_order", "getTopCompanies", "getSystemStatus", "summary", "recent_transactions", "transactions", "top_companies", "system_status", "err", "console", "interval", "setInterval", "clearInterval", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "p", "severity", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "concat", "toFixed", "toLocaleString", "netValue", "net_value", "mb", "variant", "gutterBottom", "color", "container", "spacing", "sx", "xs", "sm", "md", "title", "total_transactions", "icon", "total_buy_value", "total_sell_value", "subtitle", "unique_companies", "lg"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  CircularProgress,\n  <PERSON><PERSON>,\n  Chip,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Business,\n  Person,\n  AccountBalance,\n} from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { apiService } from '../services/apiService';\nimport StatCard from '../components/StatCard';\nimport RecentTransactions from '../components/RecentTransactions';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport LastFetchedStatus from '../components/LastFetchedStatus';\n\ninterface DashboardData {\n  summary: {\n    total_transactions: number;\n    total_buy_value: number;\n    total_sell_value: number;\n    net_value: number;\n    unique_companies: number;\n    unique_persons: number;\n    date_range: {\n      earliest: string;\n      latest: string;\n    };\n  };\n  recent_transactions: any[];\n  top_companies: any[];\n  system_status: {\n    api_status: string;\n    database_status: {\n      total_transactions: number;\n      total_companies: number;\n      date_range?: {\n        earliest: string;\n        latest: string;\n      };\n      last_updated?: string;\n    };\n    scraper_status: {\n      last_execution?: string;\n      status: string;\n      records_fetched: number;\n      records_inserted: number;\n      records_skipped: number;\n      error_message?: string;\n    };\n    timestamp: string;\n  };\n}\n\nconst Dashboard: React.FC = () => {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Fetch all dashboard data in parallel\n        const [summaryResponse, recentTransactionsResponse, topCompaniesResponse, systemStatusResponse] = await Promise.all([\n          apiService.getAnalyticsSummary(),\n          apiService.getTransactions({ limit: 10, sort_by: 'transaction_date', sort_order: 'desc' }),\n          apiService.getTopCompanies({ limit: 10 }),\n          apiService.getSystemStatus(),\n        ]);\n\n        setData({\n          summary: summaryResponse.data,\n          recent_transactions: recentTransactionsResponse.data.transactions,\n          top_companies: topCompaniesResponse.data,\n          system_status: systemStatusResponse.data,\n        });\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError('Failed to load dashboard data. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n\n    // Set up auto-refresh every 5 minutes\n    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  if (!data) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"info\">No data available</Alert>\n      </Box>\n    );\n  }\n\n  const { summary, recent_transactions, top_companies, system_status } = data;\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  const netValue = summary.net_value;\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Dashboard\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Overview of NSE insider trading activities\n        </Typography>\n      </Box>\n\n      {/* System Status */}\n      <Box mb={3}>\n        <LastFetchedStatus />\n      </Box>\n\n      {/* Key Metrics */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Total Transactions\"\n            value={summary.total_transactions.toLocaleString()}\n            icon={<AccountBalance />}\n            color=\"primary\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Buy Value\"\n            value={formatCurrency(summary.total_buy_value)}\n            icon={<TrendingUp />}\n            color=\"success\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Sell Value\"\n            value={formatCurrency(summary.total_sell_value)}\n            icon={<TrendingDown />}\n            color=\"error\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Net Value\"\n            value={formatCurrency(Math.abs(netValue))}\n            subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}\n            icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}\n            color={netValue >= 0 ? 'success' : 'error'}\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Companies\"\n            value={summary.unique_companies.toLocaleString()}\n            icon={<Business />}\n            color=\"info\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Charts and Tables */}\n      <Grid container spacing={3}>\n        {/* Top Companies Chart */}\n        <Grid size={{ xs: 12, lg: 6 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top Companies by Transaction Value\n              </Typography>\n              <TopCompaniesChart data={top_companies} />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Transaction Trends */}\n        <Grid size={{ xs: 12, lg: 6 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Transaction Trends (Last 30 Days)\n              </Typography>\n              <TransactionTrendsChart />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recent Transactions */}\n        <Grid size={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Recent Transactions\n              </Typography>\n              <RecentTransactions transactions={recent_transactions} />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,gBAAgB,CAChBC,KAAK,KAEA,eAAe,CACtB,OACEC,UAAU,CACVC,YAAY,CACZC,QAAQ,CAERC,cAAc,KACT,qBAAqB,CAE5B,OAASC,UAAU,KAAQ,wBAAwB,CACnD,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,kBAAkB,KAAM,kCAAkC,CACjE,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAC/D,MAAO,CAAAC,sBAAsB,KAAM,sCAAsC,CACzE,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAwChE,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGzB,QAAQ,CAAuB,IAAI,CAAC,CAC5D,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAgB,IAAI,CAAC,CAEvDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6B,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAACE,eAAe,CAAEC,0BAA0B,CAAEC,oBAAoB,CAAEC,oBAAoB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAClHvB,UAAU,CAACwB,mBAAmB,CAAC,CAAC,CAChCxB,UAAU,CAACyB,eAAe,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAE,kBAAkB,CAAEC,UAAU,CAAE,MAAO,CAAC,CAAC,CAC1F5B,UAAU,CAAC6B,eAAe,CAAC,CAAEH,KAAK,CAAE,EAAG,CAAC,CAAC,CACzC1B,UAAU,CAAC8B,eAAe,CAAC,CAAC,CAC7B,CAAC,CAEFlB,OAAO,CAAC,CACNmB,OAAO,CAAEb,eAAe,CAACP,IAAI,CAC7BqB,mBAAmB,CAAEb,0BAA0B,CAACR,IAAI,CAACsB,YAAY,CACjEC,aAAa,CAAEd,oBAAoB,CAACT,IAAI,CACxCwB,aAAa,CAAEd,oBAAoB,CAACV,IACtC,CAAC,CAAC,CACJ,CAAE,MAAOyB,GAAG,CAAE,CACZC,OAAO,CAACtB,KAAK,CAAC,gCAAgC,CAAEqB,GAAG,CAAC,CACpDpB,QAAQ,CAAC,kDAAkD,CAAC,CAC9D,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,kBAAkB,CAAC,CAAC,CAEpB;AACA,KAAM,CAAAqB,QAAQ,CAAGC,WAAW,CAACtB,kBAAkB,CAAE,CAAC,CAAG,EAAE,CAAG,IAAI,CAAC,CAC/D,MAAO,IAAMuB,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIzB,OAAO,CAAE,CACX,mBACEN,IAAA,CAAClB,GAAG,EAACoD,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/EtC,IAAA,CAACb,gBAAgB,EAACoD,IAAI,CAAE,EAAG,CAAE,CAAC,CAC3B,CAAC,CAEV,CAEA,GAAI/B,KAAK,CAAE,CACT,mBACER,IAAA,CAAClB,GAAG,EAAC0D,CAAC,CAAE,CAAE,CAAAF,QAAA,cACRtC,IAAA,CAACZ,KAAK,EAACqD,QAAQ,CAAC,OAAO,CAAAH,QAAA,CAAE9B,KAAK,CAAQ,CAAC,CACpC,CAAC,CAEV,CAEA,GAAI,CAACJ,IAAI,CAAE,CACT,mBACEJ,IAAA,CAAClB,GAAG,EAAC0D,CAAC,CAAE,CAAE,CAAAF,QAAA,cACRtC,IAAA,CAACZ,KAAK,EAACqD,QAAQ,CAAC,MAAM,CAAAH,QAAA,CAAC,mBAAiB,CAAO,CAAC,CAC7C,CAAC,CAEV,CAEA,KAAM,CAAEd,OAAO,CAAEC,mBAAmB,CAAEE,aAAa,CAAEC,aAAc,CAAC,CAAGxB,IAAI,CAE3E,KAAM,CAAAsC,cAAc,CAAIC,KAAgC,EAAK,CAC3D,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAIC,KAAK,CAACF,KAAK,CAAC,CAAE,CACzD,MAAO,IAAI,CACb,CAEA,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,CAChC,GAAIG,QAAQ,EAAI,QAAQ,CAAE,CACxB,eAAAG,MAAA,CAAW,CAACN,KAAK,CAAG,QAAQ,EAAEO,OAAO,CAAC,CAAC,CAAC,OAC1C,CAAC,IAAM,IAAIJ,QAAQ,EAAI,MAAM,CAAE,CAC7B,eAAAG,MAAA,CAAW,CAACN,KAAK,CAAG,MAAM,EAAEO,OAAO,CAAC,CAAC,CAAC,MACxC,CAAC,IAAM,CACL,eAAAD,MAAA,CAAWN,KAAK,CAACQ,cAAc,CAAC,CAAC,EACnC,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG5B,OAAO,CAAC6B,SAAS,CAElC,mBACEnD,KAAA,CAACpB,GAAG,EAAAwD,QAAA,eAEFpC,KAAA,CAACpB,GAAG,EAACwE,EAAE,CAAE,CAAE,CAAAhB,QAAA,eACTtC,IAAA,CAACd,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlB,QAAA,CAAC,WAEtC,CAAY,CAAC,cACbtC,IAAA,CAACd,UAAU,EAACqE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,4CAEnD,CAAY,CAAC,EACV,CAAC,cAGNtC,IAAA,CAAClB,GAAG,EAACwE,EAAE,CAAE,CAAE,CAAAhB,QAAA,cACTtC,IAAA,CAACF,iBAAiB,GAAE,CAAC,CAClB,CAAC,cAGNI,KAAA,CAACnB,IAAI,EAAC2E,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEN,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,eACxCtC,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAzB,QAAA,cACrCtC,IAAA,CAACN,QAAQ,EACPsE,KAAK,CAAC,oBAAoB,CAC1BrB,KAAK,CAAEnB,OAAO,CAACyC,kBAAkB,CAACd,cAAc,CAAC,CAAE,CACnDe,IAAI,cAAElE,IAAA,CAACR,cAAc,GAAE,CAAE,CACzBiE,KAAK,CAAC,SAAS,CAChB,CAAC,CACE,CAAC,cACPzD,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAzB,QAAA,cACrCtC,IAAA,CAACN,QAAQ,EACPsE,KAAK,CAAC,WAAW,CACjBrB,KAAK,CAAED,cAAc,CAAClB,OAAO,CAAC2C,eAAe,CAAE,CAC/CD,IAAI,cAAElE,IAAA,CAACX,UAAU,GAAE,CAAE,CACrBoE,KAAK,CAAC,SAAS,CAChB,CAAC,CACE,CAAC,cACPzD,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAzB,QAAA,cACrCtC,IAAA,CAACN,QAAQ,EACPsE,KAAK,CAAC,YAAY,CAClBrB,KAAK,CAAED,cAAc,CAAClB,OAAO,CAAC4C,gBAAgB,CAAE,CAChDF,IAAI,cAAElE,IAAA,CAACV,YAAY,GAAE,CAAE,CACvBmE,KAAK,CAAC,OAAO,CACd,CAAC,CACE,CAAC,cACPzD,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAzB,QAAA,cACrCtC,IAAA,CAACN,QAAQ,EACPsE,KAAK,CAAC,WAAW,CACjBrB,KAAK,CAAED,cAAc,CAACK,IAAI,CAACC,GAAG,CAACI,QAAQ,CAAC,CAAE,CAC1CiB,QAAQ,CAAEjB,QAAQ,EAAI,CAAC,CAAG,YAAY,CAAG,aAAc,CACvDc,IAAI,CAAEd,QAAQ,EAAI,CAAC,cAAGpD,IAAA,CAACX,UAAU,GAAE,CAAC,cAAGW,IAAA,CAACV,YAAY,GAAE,CAAE,CACxDmE,KAAK,CAAEL,QAAQ,EAAI,CAAC,CAAG,SAAS,CAAG,OAAQ,CAC5C,CAAC,CACE,CAAC,cACPpD,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAzB,QAAA,cACrCtC,IAAA,CAACN,QAAQ,EACPsE,KAAK,CAAC,WAAW,CACjBrB,KAAK,CAAEnB,OAAO,CAAC8C,gBAAgB,CAACnB,cAAc,CAAC,CAAE,CACjDe,IAAI,cAAElE,IAAA,CAACT,QAAQ,GAAE,CAAE,CACnBkE,KAAK,CAAC,MAAM,CACb,CAAC,CACE,CAAC,EACH,CAAC,cAGPvD,KAAA,CAACnB,IAAI,EAAC2E,SAAS,MAACC,OAAO,CAAE,CAAE,CAAArB,QAAA,eAEzBtC,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cAC5BtC,IAAA,CAAChB,IAAI,EAAAsD,QAAA,cACHpC,KAAA,CAACjB,WAAW,EAAAqD,QAAA,eACVtC,IAAA,CAACd,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlB,QAAA,CAAC,oCAEtC,CAAY,CAAC,cACbtC,IAAA,CAACJ,iBAAiB,EAACQ,IAAI,CAAEuB,aAAc,CAAE,CAAC,EAC/B,CAAC,CACV,CAAC,CACH,CAAC,cAGP3B,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,CAAEsB,EAAE,CAAE,EAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cAC5BtC,IAAA,CAAChB,IAAI,EAAAsD,QAAA,cACHpC,KAAA,CAACjB,WAAW,EAAAqD,QAAA,eACVtC,IAAA,CAACd,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlB,QAAA,CAAC,mCAEtC,CAAY,CAAC,cACbtC,IAAA,CAACH,sBAAsB,GAAE,CAAC,EACf,CAAC,CACV,CAAC,CACH,CAAC,cAGPG,IAAA,CAACjB,IAAI,EAACwD,IAAI,CAAE,EAAG,CAAAD,QAAA,cACbtC,IAAA,CAAChB,IAAI,EAAAsD,QAAA,cACHpC,KAAA,CAACjB,WAAW,EAAAqD,QAAA,eACVtC,IAAA,CAACd,UAAU,EAACqE,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAlB,QAAA,CAAC,qBAEtC,CAAY,CAAC,cACbtC,IAAA,CAACL,kBAAkB,EAAC+B,YAAY,CAAED,mBAAoB,CAAE,CAAC,EAC9C,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}