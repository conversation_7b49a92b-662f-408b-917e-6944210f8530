{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import{ThemeProvider,createTheme}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';// import { LocalizationProvider } from '@mui/x-date-pickers';\n// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport Layout from'./components/Layout';import Dashboard from'./pages/Dashboard';import Transactions from'./pages/Transactions';import Analytics from'./pages/Analytics';import Companies from'./pages/Companies';import'./App.css';// Create Material-UI theme\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const theme=createTheme({palette:{mode:'light',primary:{main:'#1976d2'},secondary:{main:'#dc004e'},background:{default:'#f5f5f5'}},typography:{fontFamily:'\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},components:{MuiCard:{styleOverrides:{root:{boxShadow:'0 2px 8px rgba(0,0,0,0.1)',borderRadius:8}}},MuiButton:{styleOverrides:{root:{textTransform:'none',borderRadius:6}}}}});function App(){return/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/transactions\",element:/*#__PURE__*/_jsx(Transactions,{})}),/*#__PURE__*/_jsx(Route,{path:\"/analytics\",element:/*#__PURE__*/_jsx(Analytics,{})}),/*#__PURE__*/_jsx(Route,{path:\"/companies\",element:/*#__PURE__*/_jsx(Companies,{})})]})})})]});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ThemeProvider", "createTheme", "CssBaseline", "Layout", "Dashboard", "Transactions", "Analytics", "Companies", "jsx", "_jsx", "jsxs", "_jsxs", "theme", "palette", "mode", "primary", "main", "secondary", "background", "default", "typography", "fontFamily", "h4", "fontWeight", "h5", "h6", "components", "MuiCard", "styleOverrides", "root", "boxShadow", "borderRadius", "MuiB<PERSON>on", "textTransform", "App", "children", "path", "element"], "sources": ["D:/chirag/nsescrapper/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n// import { LocalizationProvider } from '@mui/x-date-pickers';\n// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport Transactions from './pages/Transactions';\nimport Analytics from './pages/Analytics';\nimport Companies from './pages/Companies';\nimport './App.css';\n\n// Create Material-UI theme\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h4: {\n      fontWeight: 600,\n    },\n    h5: {\n      fontWeight: 600,\n    },\n    h6: {\n      fontWeight: 600,\n    },\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          borderRadius: 8,\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 6,\n        },\n      },\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      {/* <LocalizationProvider dateAdapter={AdapterDateFns}> */}\n        <Router>\n          <Layout>\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/transactions\" element={<Transactions />} />\n              <Route path=\"/analytics\" element={<Analytics />} />\n              <Route path=\"/companies\" element={<Companies />} />\n            </Routes>\n          </Layout>\n        </Router>\n      {/* </LocalizationProvider> */}\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD;AACA;AACA,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,WAAW,CAElB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAGX,WAAW,CAAC,CACxBY,OAAO,CAAE,CACPC,IAAI,CAAE,OAAO,CACbC,OAAO,CAAE,CACPC,IAAI,CAAE,SACR,CAAC,CACDC,SAAS,CAAE,CACTD,IAAI,CAAE,SACR,CAAC,CACDE,UAAU,CAAE,CACVC,OAAO,CAAE,SACX,CACF,CAAC,CACDC,UAAU,CAAE,CACVC,UAAU,CAAE,4CAA4C,CACxDC,EAAE,CAAE,CACFC,UAAU,CAAE,GACd,CAAC,CACDC,EAAE,CAAE,CACFD,UAAU,CAAE,GACd,CAAC,CACDE,EAAE,CAAE,CACFF,UAAU,CAAE,GACd,CACF,CAAC,CACDG,UAAU,CAAE,CACVC,OAAO,CAAE,CACPC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,SAAS,CAAE,2BAA2B,CACtCC,YAAY,CAAE,CAChB,CACF,CACF,CAAC,CACDC,SAAS,CAAE,CACTJ,cAAc,CAAE,CACdC,IAAI,CAAE,CACJI,aAAa,CAAE,MAAM,CACrBF,YAAY,CAAE,CAChB,CACF,CACF,CACF,CACF,CAAC,CAAC,CAEF,QAAS,CAAAG,GAAGA,CAAA,CAAG,CACb,mBACEvB,KAAA,CAACX,aAAa,EAACY,KAAK,CAAEA,KAAM,CAAAuB,QAAA,eAC1B1B,IAAA,CAACP,WAAW,GAAE,CAAC,cAEbO,IAAA,CAACZ,MAAM,EAAAsC,QAAA,cACL1B,IAAA,CAACN,MAAM,EAAAgC,QAAA,cACLxB,KAAA,CAACb,MAAM,EAAAqC,QAAA,eACL1B,IAAA,CAACV,KAAK,EAACqC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE5B,IAAA,CAACL,SAAS,GAAE,CAAE,CAAE,CAAC,cAC1CK,IAAA,CAACV,KAAK,EAACqC,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE5B,IAAA,CAACJ,YAAY,GAAE,CAAE,CAAE,CAAC,cACzDI,IAAA,CAACV,KAAK,EAACqC,IAAI,CAAC,YAAY,CAACC,OAAO,cAAE5B,IAAA,CAACH,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDG,IAAA,CAACV,KAAK,EAACqC,IAAI,CAAC,YAAY,CAACC,OAAO,cAAE5B,IAAA,CAACF,SAAS,GAAE,CAAE,CAAE,CAAC,EAC7C,CAAC,CACH,CAAC,CACH,CAAC,EAEE,CAAC,CAEpB,CAEA,cAAe,CAAA2B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}